import React from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const AccessDenied: React.FC = () => {
  const { isAdmin, isEmployee } = useAuth();
  
  // Determinar a dónde redirigir según el rol
  const redirectPath = isAdmin 
    ? '/admin/dashboard' 
    : isEmployee 
      ? '/employee/dashboard' 
      : '/login';

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-100">
      <div className="max-w-md w-full p-6 bg-white rounded-lg shadow-md text-center">
        <h1 className="text-3xl font-bold text-red-600 mb-4">Acceso Denegado</h1>
        <p className="text-gray-700 mb-6">
          No tienes permisos para acceder a esta página.
        </p>
        <Link
          to={redirectPath}
          className="inline-block px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          Volver a la página principal
        </Link>
      </div>
    </div>
  );
};

export default AccessDenied;
