<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\CashRegister;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class CashRegisterController extends Controller
{
    /**
     * Obtener todos los registros de caja.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $cashRegisters = CashRegister::with('user')->orderBy('created_at', 'desc')->get();
        
        return response()->json([
            'success' => true,
            'data' => $cashRegisters
        ]);
    }

    /**
     * Abrir una nueva caja.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function open(Request $request)
    {
        // Verificar si ya hay una caja abierta
        $openCashRegister = CashRegister::where('status', 'open')->first();
        
        if ($openCashRegister) {
            return response()->json([
                'success' => false,
                'message' => 'Ya hay una caja abierta. Debe cerrarla antes de abrir una nueva.'
            ], 422);
        }

        $validator = Validator::make($request->all(), [
            'initial_amount' => 'required|numeric|min:0',
            'details' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $cashRegister = CashRegister::create([
            'user_id' => Auth::id(),
            'initial_amount' => $request->input('initial_amount'),
            'details' => $request->input('details'),
            'status' => 'open',
            'opened_at' => now(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Caja abierta exitosamente',
            'data' => $cashRegister
        ], 201);
    }

    /**
     * Cerrar una caja.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function close(Request $request, $id)
    {
        $cashRegister = CashRegister::find($id);

        if (!$cashRegister) {
            return response()->json([
                'success' => false,
                'message' => 'Registro de caja no encontrado'
            ], 404);
        }

        if ($cashRegister->status === 'closed') {
            return response()->json([
                'success' => false,
                'message' => 'Esta caja ya está cerrada'
            ], 422);
        }

        $validator = Validator::make($request->all(), [
            'final_amount' => 'required|numeric|min:0',
            'close_details' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $cashRegister->update([
            'final_amount' => $request->input('final_amount'),
            'close_details' => $request->input('close_details'),
            'status' => 'closed',
            'closed_at' => now(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Caja cerrada exitosamente',
            'data' => $cashRegister
        ]);
    }

    /**
     * Obtener un registro de caja específico.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $cashRegister = CashRegister::with(['user', 'sales'])->find($id);

        if (!$cashRegister) {
            return response()->json([
                'success' => false,
                'message' => 'Registro de caja no encontrado'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $cashRegister
        ]);
    }

    /**
     * Obtener la caja actualmente abierta.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function current()
    {
        $cashRegister = CashRegister::where('status', 'open')->first();

        if (!$cashRegister) {
            return response()->json([
                'success' => false,
                'message' => 'No hay ninguna caja abierta actualmente'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $cashRegister
        ]);
    }
}
