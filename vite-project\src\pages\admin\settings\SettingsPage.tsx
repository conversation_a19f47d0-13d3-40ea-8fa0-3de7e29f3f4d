import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import AdminLayout from '../../../components/layouts/AdminLayout';
import {
  Settings,
  User,
  Lock,
  Store,
  DollarSign,
  Printer,
  Bell,
  Mail,
  Save,
  CheckCircle,
  AlertTriangle,
  XCircle,
  CreditCard,
  Database,
  Sun,
  Moon,
  Monitor
} from 'lucide-react';
import { useAuth } from '../../../contexts/AuthContext';
import { useTheme } from '../../../contexts/ThemeContext';
import axios from 'axios';

// Interfaces
interface StoreSettings {
  store_name: string;
  address: string;
  phone: string;
  email: string;
  tax_id: string;
  currency: string;
  tax_rate: number;
}

interface UserProfile {
  name: string;
  email: string;
  phone: string;
  avatar?: string;
}

const SettingsPage: React.FC = () => {
  const location = useLocation();
  const { user } = useAuth();
  const { theme, toggleTheme } = useTheme();
  const [activeTab, setActiveTab] = useState<string>('profile');
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Estados para perfil de usuario
  const [profile, setProfile] = useState<UserProfile>({
    name: '',
    email: '',
    phone: '',
  });
  const [currentPassword, setCurrentPassword] = useState<string>('');
  const [newPassword, setNewPassword] = useState<string>('');
  const [confirmPassword, setConfirmPassword] = useState<string>('');

  // Estados para configuración de la tienda
  const [storeSettings, setStoreSettings] = useState<StoreSettings>({
    store_name: '',
    address: '',
    phone: '',
    email: '',
    tax_id: '',
    currency: 'USD',
    tax_rate: 0,
  });

  // Estados para notificaciones
  const [emailNotifications, setEmailNotifications] = useState<boolean>(true);
  const [systemNotifications, setSystemNotifications] = useState<boolean>(true);

  // Efecto para cargar datos del usuario y configuración
  useEffect(() => {
    const fetchUserData = async () => {
      setLoading(true);
      try {
        // Obtener el token del localStorage
        const token = localStorage.getItem('token');

        // Configurar los headers
        const config = {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        };

        // En un entorno real, aquí haríamos llamadas a la API
        // const profileResponse = await axios.get('/api/user/profile', config);
        // const settingsResponse = await axios.get('/api/settings/store', config);

        // Por ahora, usamos datos de ejemplo
        setTimeout(() => {
          // Datos de perfil de ejemplo
          setProfile({
            name: user?.name || 'Admin',
            email: user?.email || '<EMAIL>',
            phone: '************',
          });

          // Datos de configuración de tienda de ejemplo
          setStoreSettings({
            store_name: 'Ferretería Pro',
            address: 'Calle Principal #123, Ciudad',
            phone: '(*************',
            email: '<EMAIL>',
            tax_id: '12-3456789',
            currency: 'USD',
            tax_rate: 16,
          });

          setLoading(false);
        }, 800);
      } catch (err: any) {
        setError('Error al cargar datos: ' + (err.response?.data?.message || err.message));
        setLoading(false);
      }
    };

    fetchUserData();

    // Determinar la pestaña activa basada en la URL
    if (location.pathname.includes('/admin/profile')) {
      setActiveTab('profile');
    } else {
      const hash = location.hash.replace('#', '');
      if (hash && ['profile', 'password', 'store', 'notifications'].includes(hash)) {
        setActiveTab(hash);
      }
    }
  }, [location, user]);

  // Función para actualizar perfil
  const handleUpdateProfile = async () => {
    setError(null);
    setSuccess(null);

    try {
      // Obtener el token del localStorage
      const token = localStorage.getItem('token');

      // Configurar los headers
      const config = {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      };

      // En un entorno real, aquí haríamos una llamada a la API
      // await axios.put('/api/user/profile', profile, config);

      // Simulamos una respuesta exitosa después de un tiempo
      setTimeout(() => {
        setSuccess('Perfil actualizado exitosamente.');
      }, 500);
    } catch (err: any) {
      setError('Error al actualizar perfil: ' + (err.response?.data?.message || err.message));
    }
  };

  // Función para cambiar contraseña
  const handleChangePassword = async () => {
    setError(null);
    setSuccess(null);

    // Validaciones
    if (!currentPassword) {
      setError('Por favor, ingresa tu contraseña actual.');
      return;
    }

    if (!newPassword) {
      setError('Por favor, ingresa una nueva contraseña.');
      return;
    }

    if (newPassword !== confirmPassword) {
      setError('Las contraseñas no coinciden.');
      return;
    }

    try {
      // Obtener el token del localStorage
      const token = localStorage.getItem('token');

      // Configurar los headers
      const config = {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      };

      // En un entorno real, aquí haríamos una llamada a la API
      // await axios.put('/api/user/password', {
      //   current_password: currentPassword,
      //   new_password: newPassword,
      // }, config);

      // Simulamos una respuesta exitosa después de un tiempo
      setTimeout(() => {
        setSuccess('Contraseña actualizada exitosamente.');
        setCurrentPassword('');
        setNewPassword('');
        setConfirmPassword('');
      }, 500);
    } catch (err: any) {
      setError('Error al cambiar contraseña: ' + (err.response?.data?.message || err.message));
    }
  };

  // Función para actualizar configuración de la tienda
  const handleUpdateStoreSettings = async () => {
    setError(null);
    setSuccess(null);

    try {
      // Obtener el token del localStorage
      const token = localStorage.getItem('token');

      // Configurar los headers
      const config = {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      };

      // En un entorno real, aquí haríamos una llamada a la API
      // await axios.put('/api/settings/store', storeSettings, config);

      // Simulamos una respuesta exitosa después de un tiempo
      setTimeout(() => {
        setSuccess('Configuración de la tienda actualizada exitosamente.');
      }, 500);
    } catch (err: any) {
      setError('Error al actualizar configuración: ' + (err.response?.data?.message || err.message));
    }
  };

  // Función para actualizar configuración de notificaciones
  const handleUpdateNotifications = async () => {
    setError(null);
    setSuccess(null);

    try {
      // Obtener el token del localStorage
      const token = localStorage.getItem('token');

      // Configurar los headers
      const config = {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      };

      // En un entorno real, aquí haríamos una llamada a la API
      // await axios.put('/api/settings/notifications', {
      //   email_notifications: emailNotifications,
      //   system_notifications: systemNotifications,
      // }, config);

      // Simulamos una respuesta exitosa después de un tiempo
      setTimeout(() => {
        setSuccess('Configuración de notificaciones actualizada exitosamente.');
      }, 500);
    } catch (err: any) {
      setError('Error al actualizar notificaciones: ' + (err.response?.data?.message || err.message));
    }
  };

  return (
    <AdminLayout>
      <div className="p-6 max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-800 mb-1">Configuración</h1>
          <p className="text-gray-600">Administra tu perfil y configura los parámetros del sistema</p>
        </div>

        {/* Messages */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg text-red-700">
            <p className="flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2" />
              {error}
            </p>
          </div>
        )}

        {success && (
          <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg text-green-700">
            <p className="flex items-center">
              <CheckCircle className="h-5 w-5 mr-2" />
              {success}
            </p>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
              <div className="p-6 border-b border-gray-100">
                <h2 className="text-lg font-bold text-gray-800">Opciones</h2>
              </div>
              <div className="p-0">
                <nav className="flex flex-col">
                  <button
                    onClick={() => setActiveTab('profile')}
                    className={`flex items-center px-6 py-3 text-sm font-medium ${
                      activeTab === 'profile'
                        ? 'bg-orange-50 text-orange-600 border-l-4 border-orange-600'
                        : 'text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    <User className={`mr-3 h-5 w-5 ${activeTab === 'profile' ? 'text-orange-600' : 'text-gray-500'}`} />
                    Perfil
                  </button>
                  <button
                    onClick={() => setActiveTab('password')}
                    className={`flex items-center px-6 py-3 text-sm font-medium ${
                      activeTab === 'password'
                        ? 'bg-orange-50 text-orange-600 border-l-4 border-orange-600'
                        : 'text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    <Lock className={`mr-3 h-5 w-5 ${activeTab === 'password' ? 'text-orange-600' : 'text-gray-500'}`} />
                    Contraseña
                  </button>
                  <button
                    onClick={() => setActiveTab('store')}
                    className={`flex items-center px-6 py-3 text-sm font-medium ${
                      activeTab === 'store'
                        ? 'bg-orange-50 text-orange-600 border-l-4 border-orange-600'
                        : 'text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    <Store className={`mr-3 h-5 w-5 ${activeTab === 'store' ? 'text-orange-600' : 'text-gray-500'}`} />
                    Tienda
                  </button>
                  <button
                    onClick={() => setActiveTab('notifications')}
                    className={`flex items-center px-6 py-3 text-sm font-medium ${
                      activeTab === 'notifications'
                        ? 'bg-orange-50 text-orange-600 border-l-4 border-orange-600'
                        : 'text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    <Bell className={`mr-3 h-5 w-5 ${activeTab === 'notifications' ? 'text-orange-600' : 'text-gray-500'}`} />
                    Notificaciones
                  </button>
                  <button
                    onClick={() => setActiveTab('appearance')}
                    className={`flex items-center px-6 py-3 text-sm font-medium ${
                      activeTab === 'appearance'
                        ? 'bg-orange-50 text-orange-600 border-l-4 border-orange-600'
                        : 'text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    <Monitor className={`mr-3 h-5 w-5 ${activeTab === 'appearance' ? 'text-orange-600' : 'text-gray-500'}`} />
                    Apariencia
                  </button>
                  <Link
                    to="/admin/settings/dni-history"
                    className="flex items-center px-6 py-3 text-sm font-medium text-gray-700 hover:bg-gray-50"
                  >
                    <Database className="mr-3 h-5 w-5 text-gray-500" />
                    Historial de DNI
                  </Link>
                </nav>
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
              {/* Profile Settings */}
              {activeTab === 'profile' && (
                <>
                  <div className="p-6 border-b border-gray-100">
                    <h2 className="text-lg font-bold text-gray-800">Información de Perfil</h2>
                  </div>
                  <div className="p-6">
                    {loading ? (
                      <div className="animate-pulse space-y-4">
                        <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                        <div className="h-10 bg-gray-200 rounded"></div>
                        <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                        <div className="h-10 bg-gray-200 rounded"></div>
                        <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                        <div className="h-10 bg-gray-200 rounded"></div>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <div>
                          <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                            Nombre Completo
                          </label>
                          <input
                            type="text"
                            id="name"
                            className="w-full p-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500"
                            value={profile.name}
                            onChange={(e) => setProfile({...profile, name: e.target.value})}
                          />
                        </div>
                        <div>
                          <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                            Correo Electrónico
                          </label>
                          <input
                            type="email"
                            id="email"
                            className="w-full p-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500"
                            value={profile.email}
                            onChange={(e) => setProfile({...profile, email: e.target.value})}
                          />
                        </div>
                        <div>
                          <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                            Teléfono
                          </label>
                          <input
                            type="tel"
                            id="phone"
                            className="w-full p-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500"
                            value={profile.phone}
                            onChange={(e) => setProfile({...profile, phone: e.target.value})}
                          />
                        </div>
                        <div className="pt-4">
                          <button
                            onClick={handleUpdateProfile}
                            className="inline-flex items-center px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 transition-colors"
                          >
                            <Save className="h-5 w-5 mr-2" />
                            Guardar Cambios
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                </>
              )}

              {/* Password Settings */}
              {activeTab === 'password' && (
                <>
                  <div className="p-6 border-b border-gray-100">
                    <h2 className="text-lg font-bold text-gray-800">Cambiar Contraseña</h2>
                  </div>
                  <div className="p-6">
                    <div className="space-y-4">
                      <div>
                        <label htmlFor="current-password" className="block text-sm font-medium text-gray-700 mb-1">
                          Contraseña Actual
                        </label>
                        <input
                          type="password"
                          id="current-password"
                          className="w-full p-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500"
                          value={currentPassword}
                          onChange={(e) => setCurrentPassword(e.target.value)}
                        />
                      </div>
                      <div>
                        <label htmlFor="new-password" className="block text-sm font-medium text-gray-700 mb-1">
                          Nueva Contraseña
                        </label>
                        <input
                          type="password"
                          id="new-password"
                          className="w-full p-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500"
                          value={newPassword}
                          onChange={(e) => setNewPassword(e.target.value)}
                        />
                      </div>
                      <div>
                        <label htmlFor="confirm-password" className="block text-sm font-medium text-gray-700 mb-1">
                          Confirmar Nueva Contraseña
                        </label>
                        <input
                          type="password"
                          id="confirm-password"
                          className="w-full p-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500"
                          value={confirmPassword}
                          onChange={(e) => setConfirmPassword(e.target.value)}
                        />
                      </div>
                      <div className="pt-4">
                        <button
                          onClick={handleChangePassword}
                          className="inline-flex items-center px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 transition-colors"
                        >
                          <Lock className="h-5 w-5 mr-2" />
                          Cambiar Contraseña
                        </button>
                      </div>
                    </div>
                  </div>
                </>
              )}

              {/* Store Settings */}
              {activeTab === 'store' && (
                <>
                  <div className="p-6 border-b border-gray-100">
                    <h2 className="text-lg font-bold text-gray-800">Configuración de la Tienda</h2>
                  </div>
                  <div className="p-6">
                    {loading ? (
                      <div className="animate-pulse space-y-4">
                        <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                        <div className="h-10 bg-gray-200 rounded"></div>
                        <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                        <div className="h-10 bg-gray-200 rounded"></div>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <div>
                          <label htmlFor="store-name" className="block text-sm font-medium text-gray-700 mb-1">
                            Nombre de la Tienda
                          </label>
                          <input
                            type="text"
                            id="store-name"
                            className="w-full p-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500"
                            value={storeSettings.store_name}
                            onChange={(e) => setStoreSettings({...storeSettings, store_name: e.target.value})}
                          />
                        </div>
                        <div>
                          <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">
                            Dirección
                          </label>
                          <input
                            type="text"
                            id="address"
                            className="w-full p-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500"
                            value={storeSettings.address}
                            onChange={(e) => setStoreSettings({...storeSettings, address: e.target.value})}
                          />
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label htmlFor="store-phone" className="block text-sm font-medium text-gray-700 mb-1">
                              Teléfono
                            </label>
                            <input
                              type="tel"
                              id="store-phone"
                              className="w-full p-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500"
                              value={storeSettings.phone}
                              onChange={(e) => setStoreSettings({...storeSettings, phone: e.target.value})}
                            />
                          </div>
                          <div>
                            <label htmlFor="store-email" className="block text-sm font-medium text-gray-700 mb-1">
                              Correo Electrónico
                            </label>
                            <input
                              type="email"
                              id="store-email"
                              className="w-full p-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500"
                              value={storeSettings.email}
                              onChange={(e) => setStoreSettings({...storeSettings, email: e.target.value})}
                            />
                          </div>
                        </div>
                        <div>
                          <label htmlFor="tax-id" className="block text-sm font-medium text-gray-700 mb-1">
                            Identificación Fiscal
                          </label>
                          <input
                            type="text"
                            id="tax-id"
                            className="w-full p-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500"
                            value={storeSettings.tax_id}
                            onChange={(e) => setStoreSettings({...storeSettings, tax_id: e.target.value})}
                          />
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label htmlFor="currency" className="block text-sm font-medium text-gray-700 mb-1">
                              Moneda
                            </label>
                            <select
                              id="currency"
                              className="w-full p-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500"
                              value={storeSettings.currency}
                              onChange={(e) => setStoreSettings({...storeSettings, currency: e.target.value})}
                            >
                              <option value="USD">USD - Dólar Estadounidense</option>
                              <option value="EUR">EUR - Euro</option>
                              <option value="MXN">MXN - Peso Mexicano</option>
                              <option value="COP">COP - Peso Colombiano</option>
                              <option value="ARS">ARS - Peso Argentino</option>
                            </select>
                          </div>
                          <div>
                            <label htmlFor="tax-rate" className="block text-sm font-medium text-gray-700 mb-1">
                              Tasa de Impuesto (%)
                            </label>
                            <input
                              type="number"
                              id="tax-rate"
                              className="w-full p-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500"
                              value={storeSettings.tax_rate}
                              onChange={(e) => setStoreSettings({...storeSettings, tax_rate: parseFloat(e.target.value) || 0})}
                              min="0"
                              max="100"
                              step="0.01"
                            />
                          </div>
                        </div>
                        <div className="pt-4">
                          <button
                            onClick={handleUpdateStoreSettings}
                            className="inline-flex items-center px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 transition-colors"
                          >
                            <Save className="h-5 w-5 mr-2" />
                            Guardar Configuración
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                </>
              )}

              {/* Notification Settings */}
              {activeTab === 'notifications' && (
                <>
                  <div className="p-6 border-b border-gray-100">
                    <h2 className="text-lg font-bold text-gray-800">Configuración de Notificaciones</h2>
                  </div>
                  <div className="p-6">
                    <div className="space-y-6">
                      <div>
                        <h3 className="text-md font-medium text-gray-900 mb-4">Notificaciones por Correo</h3>
                        <div className="space-y-3">
                          <div className="flex items-center">
                            <input
                              id="email-orders"
                              type="checkbox"
                              className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                              checked={emailNotifications}
                              onChange={(e) => setEmailNotifications(e.target.checked)}
                            />
                            <label htmlFor="email-orders" className="ml-2 block text-sm text-gray-700">
                              Recibir notificaciones de nuevas órdenes
                            </label>
                          </div>
                          <div className="flex items-center">
                            <input
                              id="email-inventory"
                              type="checkbox"
                              className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                              checked={emailNotifications}
                              onChange={(e) => setEmailNotifications(e.target.checked)}
                            />
                            <label htmlFor="email-inventory" className="ml-2 block text-sm text-gray-700">
                              Recibir alertas de inventario bajo
                            </label>
                          </div>
                        </div>
                      </div>
                      <div>
                        <h3 className="text-md font-medium text-gray-900 mb-4">Notificaciones del Sistema</h3>
                        <div className="space-y-3">
                          <div className="flex items-center">
                            <input
                              id="system-login"
                              type="checkbox"
                              className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                              checked={systemNotifications}
                              onChange={(e) => setSystemNotifications(e.target.checked)}
                            />
                            <label htmlFor="system-login" className="ml-2 block text-sm text-gray-700">
                              Notificar nuevos inicios de sesión
                            </label>
                          </div>
                          <div className="flex items-center">
                            <input
                              id="system-updates"
                              type="checkbox"
                              className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                              checked={systemNotifications}
                              onChange={(e) => setSystemNotifications(e.target.checked)}
                            />
                            <label htmlFor="system-updates" className="ml-2 block text-sm text-gray-700">
                              Notificar actualizaciones del sistema
                            </label>
                          </div>
                        </div>
                      </div>
                      <div className="pt-4">
                        <button
                          onClick={handleUpdateNotifications}
                          className="inline-flex items-center px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 transition-colors"
                        >
                          <Bell className="h-5 w-5 mr-2" />
                          Guardar Preferencias
                        </button>
                      </div>
                    </div>
                  </div>
                </>
              )}

              {/* Appearance Settings */}
              {activeTab === 'appearance' && (
                <>
                  <div className="p-6 border-b border-gray-100">
                    <h2 className="text-lg font-bold text-gray-800">Configuración de Apariencia</h2>
                  </div>
                  <div className="p-6">
                    <div className="space-y-6">
                      <div>
                        <h3 className="text-sm font-medium text-gray-700 mb-4">Tema</h3>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <button
                            onClick={() => toggleTheme()}
                            className={`p-4 rounded-lg border-2 flex flex-col items-center ${
                              theme === 'light' ? 'border-orange-500 bg-orange-50' : 'border-gray-200'
                            }`}
                          >
                            <Sun className="h-8 w-8 text-orange-500 mb-2" />
                            <span className="text-sm font-medium">Modo Claro</span>
                          </button>
                          <button
                            onClick={() => toggleTheme()}
                            className={`p-4 rounded-lg border-2 flex flex-col items-center ${
                              theme === 'dark' ? 'border-orange-500 bg-orange-50' : 'border-gray-200'
                            }`}
                          >
                            <Moon className="h-8 w-8 text-indigo-500 mb-2" />
                            <span className="text-sm font-medium">Modo Oscuro</span>
                          </button>
                          <button
                            onClick={() => {
                              // En una implementación real, aquí se detectaría la preferencia del sistema
                              // Por ahora, simplemente alternamos entre los temas
                              toggleTheme();
                            }}
                            className={`p-4 rounded-lg border-2 flex flex-col items-center border-gray-200`}
                          >
                            <Monitor className="h-8 w-8 text-gray-500 mb-2" />
                            <span className="text-sm font-medium">Usar Preferencia del Sistema</span>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
};

export default SettingsPage;
