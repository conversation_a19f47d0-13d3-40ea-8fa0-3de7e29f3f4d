"use client"

import React, { useState, useEffect } from "react"
import { <PERSON> } from "react-router-dom"
import { useAuth } from "../../contexts/AuthContext"
import AdminLayout from "../../components/layouts/AdminLayout"
import axios from "axios"
import {
  Users,
  Package,
  ShoppingCart,
  BarChart2,
  Settings,
  CircleDollarSign,
  PenToolIcon as Tool,
  AlertTriangle,
  Clock,
  Truck,
  ArrowUpRight,
  ChevronRight,
  Search,
  Plus,
} from "lucide-react"

// Interfaces para los datos
interface DashboardStats {
  inventory: number
  employees: number
  monthlySales: number
  lowStock: number
}

interface Sale {
  id: number
  product: string
  price: number
  time: string
}

interface PendingOrder {
  id: number
  supplier: string
  items: number
  date: string
}

const AdminDashboard: React.FC = () => {
  const { user } = useAuth()
  const [stats, setStats] = useState<DashboardStats>({
    inventory: 0,
    employees: 0,
    monthlySales: 0,
    lowStock: 0,
  })
  const [recentSales, setRecentSales] = useState<Sale[]>([])
  const [pendingOrders, setPendingOrders] = useState<PendingOrder[]>([])
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)

  // Función para obtener la hora del día y un saludo personalizado
  const getGreeting = () => {
    const hour = new Date().getHours()
    if (hour < 12) return "Buenos días"
    if (hour < 18) return "Buenas tardes"
    return "Buenas noches"
  }

  // Efecto para cargar los datos del dashboard
  useEffect(() => {
    const fetchDashboardData = async () => {
      setLoading(true)
      try {
        // Obtener el token del localStorage
        const token = localStorage.getItem('token')

        // Configurar los headers
        const config = {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }

        // Intentar obtener datos reales del backend
        // Si las APIs no están disponibles, usamos datos de ejemplo

        // Obtener estadísticas
        let statsData = {
          inventory: 1248,
          employees: 8,
          monthlySales: 45680,
          lowStock: 24,
        }

        try {
          // Intentar obtener el conteo de empleados
          const employeesResponse = await axios.get('/api/employees', config)
          if (employeesResponse.data) {
            statsData.employees = employeesResponse.data.length
          }
        } catch (err) {
          console.log('Error al obtener empleados, usando datos de ejemplo')
        }

        // Actualizar estadísticas
        setStats(statsData)

        // Datos de ejemplo para ventas recientes (hasta que haya una API real)
        const salesData = [
          { id: 1, product: "Taladro Eléctrico 750W", price: 89.99, time: "10:45 AM" },
          { id: 2, product: "Juego de Destornilladores", price: 34.5, time: "09:32 AM" },
          { id: 3, product: "Pintura Látex 20L", price: 75.0, time: "09:15 AM" },
          { id: 4, product: "Cerradura de Seguridad", price: 45.75, time: "Ayer" },
          { id: 5, product: "Escalera Plegable 5m", price: 120.0, time: "Ayer" },
        ]
        setRecentSales(salesData)

        // Datos de ejemplo para órdenes pendientes (hasta que haya una API real)
        const ordersData = [
          { id: 101, supplier: "Herramientas Industriales S.A.", items: 12, date: "15/05/2023" },
          { id: 102, supplier: "Pinturas del Norte", items: 8, date: "18/05/2023" },
          { id: 103, supplier: "Ferretería Mayorista", items: 15, date: "20/05/2023" },
        ]
        setPendingOrders(ordersData)

        setError(null)
      } catch (err: any) {
        setError('Error al cargar datos del dashboard: ' + (err.response?.data?.message || err.message))
      } finally {
        setLoading(false)
      }
    }

    fetchDashboardData()
  }, [])

  return (
    <AdminLayout>
      <div className="p-6 max-w-7xl mx-auto">
        {/* Welcome Section */}
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-800 mb-1">
            {getGreeting()}, {user?.name}
          </h1>
          <p className="text-gray-600">
            Bienvenido al panel de administración de Ferretería Pro. Aquí tienes un resumen de tu negocio.
          </p>
        </div>

        {/* Error message */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg text-red-700">
            <p className="flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2" />
              {error}
            </p>
          </div>
        )}

        {/* Stats Cards */}
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[...Array(4)].map((_, index) => (
              <div key={index} className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <div className="animate-pulse flex justify-between">
                  <div className="space-y-3 w-2/3">
                    <div className="h-2 bg-gray-200 rounded"></div>
                    <div className="h-6 bg-gray-200 rounded w-1/2"></div>
                    <div className="h-2 bg-gray-200 rounded w-1/3"></div>
                  </div>
                  <div className="h-12 w-12 bg-gray-200 rounded-lg"></div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
              <div className="flex justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">Productos en inventario</p>
                  <p className="text-2xl font-bold text-gray-800 mt-1">{stats.inventory.toLocaleString()}</p>
                  <div className="flex items-center mt-2 text-xs font-medium text-green-600">
                    <ArrowUpRight className="h-3 w-3 mr-1" />
                    <span>12% más que el mes pasado</span>
                  </div>
                </div>
                <div className="h-12 w-12 bg-blue-50 rounded-lg flex items-center justify-center">
                  <Package className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
              <div className="flex justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">Empleados activos</p>
                  <p className="text-2xl font-bold text-gray-800 mt-1">{stats.employees}</p>
                  <div className="flex items-center mt-2 text-xs font-medium text-gray-600">
                    <span>Datos actualizados en tiempo real</span>
                  </div>
                </div>
                <div className="h-12 w-12 bg-indigo-50 rounded-lg flex items-center justify-center">
                  <Users className="h-6 w-6 text-indigo-600" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
              <div className="flex justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">Ventas del mes</p>
                  <p className="text-2xl font-bold text-gray-800 mt-1">${stats.monthlySales.toLocaleString()}</p>
                  <div className="flex items-center mt-2 text-xs font-medium text-green-600">
                    <ArrowUpRight className="h-3 w-3 mr-1" />
                    <span>8.2% más que el mes pasado</span>
                  </div>
                </div>
                <div className="h-12 w-12 bg-green-50 rounded-lg flex items-center justify-center">
                  <CircleDollarSign className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
              <div className="flex justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">Productos por reponer</p>
                  <p className="text-2xl font-bold text-gray-800 mt-1">{stats.lowStock}</p>
                  <div className="flex items-center mt-2 text-xs font-medium text-red-600">
                    <AlertTriangle className="h-3 w-3 mr-1" />
                    <span>{stats.lowStock} productos críticos</span>
                  </div>
                </div>
                <div className="h-12 w-12 bg-red-50 rounded-lg flex items-center justify-center">
                  <AlertTriangle className="h-6 w-6 text-red-600" />
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column */}
          <div className="lg:col-span-2 space-y-6">
            {/* Recent Sales */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
              <div className="p-6 border-b border-gray-100">
                <div className="flex items-center justify-between">
                  <h2 className="text-lg font-bold text-gray-800">Ventas Recientes</h2>
                  <Link
                    to="/admin/sales"
                    className="text-sm font-medium text-orange-600 hover:text-orange-700 flex items-center"
                  >
                    Ver todas <ChevronRight className="h-4 w-4 ml-1" />
                  </Link>
                </div>
              </div>
              <div className="overflow-x-auto">
                {loading ? (
                  <div className="animate-pulse p-6">
                    <div className="space-y-4">
                      {[...Array(5)].map((_, index) => (
                        <div key={index} className="flex items-center justify-between">
                          <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                          <div className="h-4 bg-gray-200 rounded w-20"></div>
                          <div className="h-4 bg-gray-200 rounded w-24"></div>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <table className="w-full">
                    <thead>
                      <tr className="bg-gray-50">
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Producto
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Precio
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Hora
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {recentSales.map((sale) => (
                        <tr key={sale.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800">
                            {sale.product}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">${sale.price.toFixed(2)}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 flex items-center">
                            <Clock className="h-3 w-3 mr-1 text-gray-400" />
                            {sale.time}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                )}
              </div>
            </div>

            {/* Pending Orders */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
              <div className="p-6 border-b border-gray-100">
                <div className="flex items-center justify-between">
                  <h2 className="text-lg font-bold text-gray-800">Órdenes de Compra Pendientes</h2>
                  <Link
                    to="/admin/purchases"
                    className="text-sm font-medium text-orange-600 hover:text-orange-700 flex items-center"
                  >
                    Ver todas <ChevronRight className="h-4 w-4 ml-1" />
                  </Link>
                </div>
              </div>
              <div className="overflow-x-auto">
                {loading ? (
                  <div className="animate-pulse p-6">
                    <div className="space-y-4">
                      {[...Array(3)].map((_, index) => (
                        <div key={index} className="flex items-center justify-between">
                          <div className="h-4 bg-gray-200 rounded w-16"></div>
                          <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                          <div className="h-4 bg-gray-200 rounded w-12"></div>
                          <div className="h-4 bg-gray-200 rounded w-24"></div>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <table className="w-full">
                    <thead>
                      <tr className="bg-gray-50">
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Orden #
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Proveedor
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Items
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Fecha
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {pendingOrders.map((order) => (
                        <tr key={order.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800">#{order.id}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">{order.supplier}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">{order.items}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 flex items-center">
                            <Truck className="h-3 w-3 mr-1 text-gray-400" />
                            {order.date}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                )}
              </div>
            </div>
          </div>

          {/* Right Column */}
          <div className="space-y-6">
            {/* Quick Actions */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
              <h2 className="text-lg font-bold text-gray-800 mb-4">Acciones Rápidas</h2>
              <div className="space-y-3">
                <Link
                  to="/admin/inventory/add"
                  className="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <div className="h-8 w-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                    <Package className="h-4 w-4 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-800">Agregar Producto</p>
                    <p className="text-xs text-gray-500">Añadir nuevo producto al inventario</p>
                  </div>
                </Link>

                <Link
                  to="/admin/cashier/open"
                  className="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <div className="h-8 w-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                    <CircleDollarSign className="h-4 w-4 text-green-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-800">Abrir Caja</p>
                    <p className="text-xs text-gray-500">Iniciar operaciones de caja</p>
                  </div>
                </Link>

                <Link
                  to="/admin/purchases/new"
                  className="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <div className="h-8 w-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                    <ShoppingCart className="h-4 w-4 text-purple-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-800">Nueva Venta</p>
                    <p className="text-xs text-gray-500">Registrar venta y actualizar inventario</p>
                  </div>
                </Link>

                <Link
                  to="/admin/reports/generate"
                  className="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <div className="h-8 w-8 bg-amber-100 rounded-lg flex items-center justify-center mr-3">
                    <BarChart2 className="h-4 w-4 text-amber-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-800">Generar Reporte</p>
                    <p className="text-xs text-gray-500">Crear informes de ventas e inventario</p>
                  </div>
                </Link>
              </div>
            </div>

            {/* Low Stock Alert */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-bold text-gray-800">Alerta de Stock</h2>
                <span className="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                  5 críticos
                </span>
              </div>
              <div className="space-y-3">
                <div className="p-3 bg-red-50 border border-red-100 rounded-lg">
                  <div className="flex justify-between items-center">
                    <p className="text-sm font-medium text-gray-800">Tornillos 1/4" x 2"</p>
                    <span className="text-xs font-bold text-red-600">2 unid.</span>
                  </div>
                  <div className="mt-1 flex justify-between items-center">
                    <p className="text-xs text-gray-500">Stock mínimo: 50 unid.</p>
                    <button className="text-xs text-white bg-red-600 hover:bg-red-700 px-2 py-1 rounded flex items-center">
                      <Plus className="h-3 w-3 mr-1" /> Reponer
                    </button>
                  </div>
                </div>

                <div className="p-3 bg-red-50 border border-red-100 rounded-lg">
                  <div className="flex justify-between items-center">
                    <p className="text-sm font-medium text-gray-800">Pintura Látex Blanco</p>
                    <span className="text-xs font-bold text-red-600">1 unid.</span>
                  </div>
                  <div className="mt-1 flex justify-between items-center">
                    <p className="text-xs text-gray-500">Stock mínimo: 10 unid.</p>
                    <button className="text-xs text-white bg-red-600 hover:bg-red-700 px-2 py-1 rounded flex items-center">
                      <Plus className="h-3 w-3 mr-1" /> Reponer
                    </button>
                  </div>
                </div>

                <div className="p-3 bg-amber-50 border border-amber-100 rounded-lg">
                  <div className="flex justify-between items-center">
                    <p className="text-sm font-medium text-gray-800">Cemento Portland 50kg</p>
                    <span className="text-xs font-bold text-amber-600">8 unid.</span>
                  </div>
                  <div className="mt-1 flex justify-between items-center">
                    <p className="text-xs text-gray-500">Stock mínimo: 15 unid.</p>
                    <button className="text-xs text-white bg-amber-600 hover:bg-amber-700 px-2 py-1 rounded flex items-center">
                      <Plus className="h-3 w-3 mr-1" /> Reponer
                    </button>
                  </div>
                </div>
              </div>
              <Link
                to="/admin/inventory/low-stock"
                className="mt-4 text-sm font-medium text-orange-600 hover:text-orange-700 flex items-center justify-center"
              >
                Ver todos los productos con bajo stock <ChevronRight className="h-4 w-4 ml-1" />
              </Link>
            </div>

            {/* Search Products */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
              <h2 className="text-lg font-bold text-gray-800 mb-4">Búsqueda Rápida</h2>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-orange-500 focus:border-orange-500 block w-full pl-10 p-2.5"
                  placeholder="Buscar productos, clientes..."
                />
              </div>
            </div>
          </div>
        </div>

        {/* System Modules */}
        <h2 className="text-xl font-bold text-gray-800 mt-8 mb-6 flex items-center">
          <Tool className="mr-2 h-5 w-5 text-orange-600" />
          Módulos del Sistema
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {/* Gestión de Empleados */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-all duration-300">
            <div className="p-6 border-b border-gray-100">
              <div className="flex items-center">
                <div className="h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                  <Users className="h-5 w-5 text-blue-600" />
                </div>
                <h3 className="text-lg font-bold text-gray-800">Empleados</h3>
              </div>
            </div>
            <div className="p-6">
              <p className="text-gray-600 mb-6 h-16">
                Administra los empleados de la ferretería. Crea, edita y elimina cuentas de usuarios.
              </p>
              <Link
                to="/admin/employees"
                className="inline-flex items-center justify-center w-full px-4 py-2 bg-gray-100 text-gray-800 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-300 transition-colors"
              >
                Gestionar Empleados
              </Link>
            </div>
          </div>

          {/* Inventario */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-all duration-300">
            <div className="p-6 border-b border-gray-100">
              <div className="flex items-center">
                <div className="h-10 w-10 bg-amber-100 rounded-lg flex items-center justify-center mr-4">
                  <Package className="h-5 w-5 text-amber-600" />
                </div>
                <h3 className="text-lg font-bold text-gray-800">Inventario</h3>
              </div>
            </div>
            <div className="p-6">
              <p className="text-gray-600 mb-6 h-16">
                Gestiona el inventario de productos, categorías, stock y precios.
              </p>
              <Link
                to="/admin/inventory"
                className="inline-flex items-center justify-center w-full px-4 py-2 bg-amber-600 text-white rounded-lg hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-amber-500 transition-colors"
              >
                Gestionar Inventario
              </Link>
            </div>
          </div>

          {/* Apertura de Caja */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-all duration-300">
            <div className="p-6 border-b border-gray-100">
              <div className="flex items-center">
                <div className="h-10 w-10 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                  <CircleDollarSign className="h-5 w-5 text-purple-600" />
                </div>
                <h3 className="text-lg font-bold text-gray-800">Apertura de Caja</h3>
              </div>
            </div>
            <div className="p-6">
              <p className="text-gray-600 mb-6 h-16">
                Gestiona la apertura y cierre de caja, registra movimientos y balances.
              </p>
              <Link
                to="/admin/cashier"
                className="inline-flex items-center justify-center w-full px-4 py-2 bg-gray-100 text-gray-800 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-300 transition-colors"
              >
                Gestionar Caja
              </Link>
            </div>
          </div>

          {/* Compras */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-all duration-300">
            <div className="p-6 border-b border-gray-100">
              <div className="flex items-center">
                <div className="h-10 w-10 bg-orange-100 rounded-lg flex items-center justify-center mr-4">
                  <ShoppingCart className="h-5 w-5 text-orange-600" />
                </div>
                <h3 className="text-lg font-bold text-gray-800">Compras</h3>
              </div>
            </div>
            <div className="p-6">
              <p className="text-gray-600 mb-6 h-16">
                Gestiona las compras a proveedores, órdenes de compra y recepción de mercadería.
              </p>
              <Link
                to="/admin/purchases"
                className="inline-flex items-center justify-center w-full px-4 py-2 bg-gray-100 text-gray-800 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-300 transition-colors"
              >
                Gestionar Compras
              </Link>
            </div>
          </div>

          {/* Reportes */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-all duration-300">
            <div className="p-6 border-b border-gray-100">
              <div className="flex items-center">
                <div className="h-10 w-10 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                  <BarChart2 className="h-5 w-5 text-green-600" />
                </div>
                <h3 className="text-lg font-bold text-gray-800">Reportes</h3>
              </div>
            </div>
            <div className="p-6">
              <p className="text-gray-600 mb-6 h-16">
                Genera reportes de ventas, inventario, compras y rendimiento del negocio.
              </p>
              <Link
                to="/admin/reports"
                className="inline-flex items-center justify-center w-full px-4 py-2 bg-gray-100 text-gray-800 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-300 transition-colors"
              >
                Ver Reportes
              </Link>
            </div>
          </div>

          {/* Configuración */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-all duration-300">
            <div className="p-6 border-b border-gray-100">
              <div className="flex items-center">
                <div className="h-10 w-10 bg-gray-100 rounded-lg flex items-center justify-center mr-4">
                  <Settings className="h-5 w-5 text-gray-600" />
                </div>
                <h3 className="text-lg font-bold text-gray-800">Configuración</h3>
              </div>
            </div>
            <div className="p-6">
              <p className="text-gray-600 mb-6 h-16">
                Configura los parámetros del sistema, impuestos, moneda y preferencias.
              </p>
              <Link
                to="/admin/settings"
                className="inline-flex items-center justify-center w-full px-4 py-2 bg-gray-100 text-gray-800 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-300 transition-colors"
              >
                Configurar Sistema
              </Link>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  )
}

export default AdminDashboard
