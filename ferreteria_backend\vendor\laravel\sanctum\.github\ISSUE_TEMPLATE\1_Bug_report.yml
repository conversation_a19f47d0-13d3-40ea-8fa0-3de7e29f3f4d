name: Bug Report
description: "Report something that's broken."
body:
  - type: markdown
    attributes:
      value: "Please read [our full contribution guide](https://laravel.com/docs/contributions#bug-reports) before submitting bug reports. If you notice improper DocBlock, PHPStan, or IDE warnings while using Laravel, do not create a GitHub issue. Instead, please submit a pull request to fix the problem."
  - type: input
    attributes:
      label: Sanctum Version
      description: Provide the Sanctum version that you are using.
      placeholder: 1.6.1
    validations:
      required: true
  - type: input
    attributes:
      label: Laravel Version
      description: Provide the Laravel version that you are using. [Please ensure it is still supported.](https://laravel.com/docs/releases#support-policy)
      placeholder: 10.4.1
    validations:
      required: true
  - type: input
    attributes:
      label: PHP Version
      description: Provide the PHP version that you are using.
      placeholder: 8.1.4
    validations:
      required: true
  - type: input
    attributes:
      label: Database Driver & Version
      description: If applicable, provide the database driver and version you are using.
      placeholder: "MySQL 8.0.31 for macOS 13.0 on arm64 (Homebrew)"
    validations:
      required: false
  - type: textarea
    attributes:
      label: Description
      description: Provide a detailed description of the issue you are facing.
    validations:
      required: true
  - type: textarea
    attributes:
      label: Steps To Reproduce
      description: Provide detailed steps to reproduce your issue. If necessary, please provide a GitHub repository to demonstrate your issue using `laravel new bug-report --github="--public"`.
    validations:
      required: true
      
