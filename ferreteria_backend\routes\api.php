<?php

use App\Http\Controllers\API\AuthController;
use App\Http\Controllers\API\CashRegisterController;
use App\Http\Controllers\API\CustomerController;
use App\Http\Controllers\API\DniConsultController;
use App\Http\Controllers\API\EmployeeController;
use App\Http\Controllers\API\ProductCategoryController;
use App\Http\Controllers\API\ProductController;
use App\Http\Controllers\API\SaleController;
use App\Http\Controllers\API\SupplierController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Rutas públicas
Route::post('/login', [AuthController::class, 'login']);
Route::get('/consultar-dni/{dni}', [DniConsultController::class, 'consultarDni']);
Route::get('/consultas-dni', [DniConsultController::class, 'index']);

// Rutas de la API (sin autenticación por ahora para debugging)
// Route::middleware('auth:sanctum')->group(function () {
    // Usuario actual
    Route::get('/user', [AuthController::class, 'user']);
    Route::post('/logout', [AuthController::class, 'logout']);

    // Empleados
    Route::apiResource('employees', EmployeeController::class);

    // Categorías de productos
    Route::apiResource('product-categories', ProductCategoryController::class);

    // Productos
    Route::apiResource('products', ProductController::class);
    Route::get('/products/low-stock', [ProductController::class, 'lowStock']);
    Route::put('/products/{id}/stock', [ProductController::class, 'updateStock']);

    // Clientes
    Route::apiResource('customers', CustomerController::class);
    Route::get('/customers/document/{type}/{number}', [CustomerController::class, 'findByDocument']);

    // Apertura de caja
    Route::get('/cash-registers', [CashRegisterController::class, 'index']);
    Route::get('/cash-registers/current', [CashRegisterController::class, 'current']);
    Route::get('/cash-registers/{id}', [CashRegisterController::class, 'show']);
    Route::post('/cash-registers/open', [CashRegisterController::class, 'open']);
    Route::put('/cash-registers/{id}/close', [CashRegisterController::class, 'close']);

    // Ventas
    Route::get('/sales', [SaleController::class, 'index']);
    Route::post('/sales', [SaleController::class, 'store']);
    Route::get('/sales/{id}', [SaleController::class, 'show']);
    Route::put('/sales/{id}/cancel', [SaleController::class, 'cancel']);

    // Proveedores
    Route::apiResource('suppliers', SupplierController::class);

    // Consulta de DNI
    Route::get('customer/consultar-dni/{dni}', [DniConsultController::class, 'consultarDni']);

    // Historial de consultas de DNI
    Route::get('dni-history', [DniConsultController::class, 'index']);
    Route::delete('dni-history', [DniConsultController::class, 'clearHistory']);
// });
