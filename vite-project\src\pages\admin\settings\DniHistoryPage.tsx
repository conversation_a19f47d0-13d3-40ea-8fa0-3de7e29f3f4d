import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import AdminLayout from '../../../components/layouts/AdminLayout';
import { ArrowLeft, CreditCard, Search, Trash2 } from 'lucide-react';
import api, { API_ROUTES } from '../../../utils/api';

interface DniConsult {
  dni: string;
  nombres: string;
  apellidos: string;
  fullName: string;
  timestamp?: string;
}

const DniHistoryPage: React.FC = () => {
  const [dniConsults, setDniConsults] = useState<DniConsult[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [error, setError] = useState<string | null>(null);

  // Cargar datos del backend
  useEffect(() => {
    const fetchDniHistory = async () => {
      try {
        const response = await api.get(API_ROUTES.DNI_HISTORY);

        if (response.data.success) {
          setDniConsults(response.data.data);
        } else {
          setError('Error al cargar el historial de consultas');
        }
      } catch (err: any) {
        console.error('Error al cargar historial de DNI:', err);
        // Si no hay API disponible, cargar desde localStorage como fallback
        try {
          const storedData = localStorage.getItem('dniConsults');
          console.log('Datos del localStorage:', storedData);

          if (storedData) {
            const parsedData = JSON.parse(storedData);
            console.log('Datos parseados:', parsedData);
            setDniConsults(parsedData);
          }
        } catch (localErr: any) {
          console.error('Error al cargar datos del localStorage:', localErr);
          setError('Error al cargar el historial: ' + localErr.message);
        }
      }
    };

    fetchDniHistory();
  }, []);

  // Filtrar consultas por término de búsqueda
  const filteredConsults = dniConsults.filter(consult => {
    if (!searchTerm) return true;

    const searchLower = searchTerm.toLowerCase();
    return (
      consult.dni?.includes(searchTerm) ||
      consult.fullName?.toLowerCase().includes(searchLower) ||
      consult.nombres?.toLowerCase().includes(searchLower) ||
      consult.apellidos?.toLowerCase().includes(searchLower)
    );
  });

  // Función para limpiar el historial
  const handleClearHistory = async () => {
    if (window.confirm('¿Estás seguro de que deseas eliminar todo el historial de consultas?')) {
      try {
        const response = await api.delete(API_ROUTES.DNI_HISTORY);

        if (response.data.success) {
          setDniConsults([]);
          // También limpiar localStorage como fallback
          localStorage.removeItem('dniConsults');
        } else {
          setError('Error al limpiar el historial');
        }
      } catch (err: any) {
        console.error('Error al limpiar historial:', err);
        // Si la API no está disponible, limpiar solo localStorage
        localStorage.removeItem('dniConsults');
        setDniConsults([]);
      }
    }
  };

  return (
    <AdminLayout>
      <div className="p-6 max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-800 mb-1">Historial de Consultas DNI</h1>
            <p className="text-gray-600">Registro de todas las consultas de DNI realizadas</p>
          </div>
          <div className="mt-4 md:mt-0 flex space-x-3">
            <button
              onClick={handleClearHistory}
              disabled={dniConsults.length === 0}
              className={`inline-flex items-center px-4 py-2 border border-transparent rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                dniConsults.length === 0
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500'
              }`}
            >
              <Trash2 className="h-5 w-5 mr-2" /> Limpiar Historial
            </button>
            <Link
              to="/admin/settings"
              className="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors"
            >
              <ArrowLeft className="h-5 w-5 mr-2" /> Volver
            </Link>
          </div>
        </div>

        {/* Error message */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg text-red-700">
            <p>{error}</p>
          </div>
        )}

        {/* Search */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-6">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
              placeholder="Buscar por DNI o nombre..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        {/* DNI Consults Table */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
          <div className="p-6 border-b border-gray-100">
            <div className="flex items-center">
              <CreditCard className="h-5 w-5 text-gray-500 mr-2" />
              <h2 className="text-lg font-bold text-gray-800">Consultas Realizadas</h2>
            </div>
          </div>

          {filteredConsults.length === 0 ? (
            <div className="p-6 text-center">
              {searchTerm ? (
                <p className="text-gray-500">No se encontraron consultas que coincidan con la búsqueda.</p>
              ) : (
                <p className="text-gray-500">No hay consultas de DNI registradas.</p>
              )}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="bg-gray-50">
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      DNI
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Nombre Completo
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Nombres
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Apellidos
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {filteredConsults.map((consult, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center text-sm font-medium text-gray-900">
                          <CreditCard className="h-4 w-4 text-gray-400 mr-2" />
                          {consult.dni}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{consult.fullName}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-600">{consult.nombres}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-600">{consult.apellidos}</div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </AdminLayout>
  );
};

export default DniHistoryPage;
