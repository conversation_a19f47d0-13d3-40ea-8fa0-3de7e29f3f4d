<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;

class EmployeeController extends Controller
{
    /**
     * Constructor del controlador.
     */
    public function __construct()
    {
        // Asegurar que solo los administradores puedan acceder a estas funciones
        $this->middleware('auth');
        $this->middleware('admin');
    }

    /**
     * Mostrar la lista de empleados.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $employees = User::where('role', 'employee')->get();
        return view('admin.employees.index', compact('employees'));
    }

    /**
     * Mostrar el formulario para crear un nuevo empleado.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('admin.employees.create');
    }

    /**
     * Almacenar un nuevo empleado en la base de datos.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'phone' => 'nullable|string|max:20',
            'dni' => 'nullable|string|max:20',
            'password' => 'required|string|min:6',
        ]);

        $validated['role'] = 'employee';
        $validated['password'] = Hash::make($validated['password']);

        User::create($validated);

        return redirect()->route('admin.employees.index')
            ->with('success', 'Empleado creado exitosamente.');
    }

    /**
     * Mostrar el formulario para editar un empleado.
     *
     * @param  \App\Models\User  $employee
     * @return \Illuminate\View\View
     */
    public function edit(User $employee)
    {
        // Verificar que el usuario es un empleado
        if (!$employee->isEmployee()) {
            abort(404);
        }

        return view('admin.employees.edit', compact('employee'));
    }

    /**
     * Actualizar el empleado especificado en la base de datos.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\User  $employee
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, User $employee)
    {
        // Verificar que el usuario es un empleado
        if (!$employee->isEmployee()) {
            abort(404);
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => [
                'required',
                'string',
                'email',
                'max:255',
                Rule::unique('users')->ignore($employee->id),
            ],
            'phone' => 'nullable|string|max:20',
            'dni' => 'nullable|string|max:20',
            'password' => 'nullable|string|min:6',
        ]);

        // Solo actualizar la contraseña si se proporciona
        if (isset($validated['password']) && $validated['password']) {
            $validated['password'] = Hash::make($validated['password']);
        } else {
            unset($validated['password']);
        }

        $employee->update($validated);

        return redirect()->route('admin.employees.index')
            ->with('success', 'Empleado actualizado exitosamente.');
    }

    /**
     * Eliminar el empleado especificado de la base de datos.
     *
     * @param  \App\Models\User  $employee
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(User $employee)
    {
        // Verificar que el usuario es un empleado
        if (!$employee->isEmployee()) {
            abort(404);
        }

        $employee->delete();

        return redirect()->route('admin.employees.index')
            ->with('success', 'Empleado eliminado exitosamente.');
    }
}
