<?php

namespace Database\Seeders;

use App\Models\Product;
use App\Models\ProductCategory;
use Illuminate\Database\Seeder;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Obtener las categorías
        $herramientas = ProductCategory::where('name', 'Herramientas')->first()->id;
        $materiales = ProductCategory::where('name', 'Materiales de Construcción')->first()->id;
        $electricidad = ProductCategory::where('name', 'Electricidad')->first()->id;
        $plomeria = ProductCategory::where('name', 'Plomería')->first()->id;
        $pinturas = ProductCategory::where('name', 'Pinturas')->first()->id;
        $ferreteria = ProductCategory::where('name', 'Ferretería')->first()->id;
        $jardineria = ProductCategory::where('name', 'Jardinería')->first()->id;
        $seguridad = ProductCategory::where('name', 'Seguridad')->first()->id;

        // Productos
        $products = [
            // Herramientas
            [
                'code' => 'HER-001',
                'name' => 'Martillo de Carpintero',
                'description' => 'Martillo de carpintero con mango de madera, 16 oz',
                'category_id' => $herramientas,
                'price' => 15.99,
                'stock' => 45,
                'min_stock' => 10,
                'active' => true,
            ],
            [
                'code' => 'HER-002',
                'name' => 'Destornillador Phillips',
                'description' => 'Destornillador Phillips #2 con mango ergonómico',
                'category_id' => $herramientas,
                'price' => 8.50,
                'stock' => 60,
                'min_stock' => 15,
                'active' => true,
            ],
            [
                'code' => 'HER-003',
                'name' => 'Llave Ajustable 10"',
                'description' => 'Llave ajustable de 10 pulgadas con mango antideslizante',
                'category_id' => $herramientas,
                'price' => 12.99,
                'stock' => 25,
                'min_stock' => 8,
                'active' => true,
            ],
            [
                'code' => 'HER-004',
                'name' => 'Taladro Eléctrico 750W',
                'description' => 'Taladro eléctrico de 750W con mandril de 13mm',
                'category_id' => $herramientas,
                'price' => 89.99,
                'stock' => 10,
                'min_stock' => 3,
                'active' => true,
            ],
            [
                'code' => 'HER-005',
                'name' => 'Cinta Métrica 5m',
                'description' => 'Cinta métrica de 5 metros con bloqueo automático',
                'category_id' => $herramientas,
                'price' => 7.25,
                'stock' => 40,
                'min_stock' => 10,
                'active' => true,
            ],
            
            // Materiales de Construcción
            [
                'code' => 'MAT-001',
                'name' => 'Cemento Portland 50kg',
                'description' => 'Bolsa de cemento Portland de 50kg para construcción',
                'category_id' => $materiales,
                'price' => 12.75,
                'stock' => 8,
                'min_stock' => 15,
                'active' => true,
            ],
            [
                'code' => 'MAT-002',
                'name' => 'Ladrillo Común (u)',
                'description' => 'Ladrillo común para construcción, precio por unidad',
                'category_id' => $materiales,
                'price' => 0.35,
                'stock' => 1200,
                'min_stock' => 500,
                'active' => true,
            ],
            
            // Electricidad
            [
                'code' => 'ELE-001',
                'name' => 'Cable Eléctrico 12AWG (m)',
                'description' => 'Cable eléctrico 12AWG, precio por metro',
                'category_id' => $electricidad,
                'price' => 1.25,
                'stock' => 200,
                'min_stock' => 50,
                'active' => true,
            ],
            [
                'code' => 'ELE-002',
                'name' => 'Interruptor Simple',
                'description' => 'Interruptor simple para instalación eléctrica',
                'category_id' => $electricidad,
                'price' => 3.99,
                'stock' => 50,
                'min_stock' => 15,
                'active' => true,
            ],
            
            // Plomería
            [
                'code' => 'PLO-001',
                'name' => 'Tubo PVC 1/2" x 3m',
                'description' => 'Tubo de PVC de 1/2 pulgada por 3 metros',
                'category_id' => $plomeria,
                'price' => 4.99,
                'stock' => 35,
                'min_stock' => 10,
                'active' => true,
            ],
            [
                'code' => 'PLO-002',
                'name' => 'Llave de Paso 1/2"',
                'description' => 'Llave de paso de 1/2 pulgada para instalación de agua',
                'category_id' => $plomeria,
                'price' => 8.75,
                'stock' => 30,
                'min_stock' => 10,
                'active' => true,
            ],
            
            // Pinturas
            [
                'code' => 'PIN-001',
                'name' => 'Pintura Látex Blanco 20L',
                'description' => 'Pintura látex blanca para interiores, balde de 20 litros',
                'category_id' => $pinturas,
                'price' => 45.00,
                'stock' => 12,
                'min_stock' => 5,
                'active' => true,
            ],
            [
                'code' => 'PIN-002',
                'name' => 'Pintura Esmalte Negro 1L',
                'description' => 'Pintura esmalte negra para exteriores, lata de 1 litro',
                'category_id' => $pinturas,
                'price' => 12.50,
                'stock' => 18,
                'min_stock' => 5,
                'active' => true,
            ],
            
            // Ferretería
            [
                'code' => 'FER-001',
                'name' => 'Tornillos 1/4" x 2" (100u)',
                'description' => 'Tornillos de 1/4 x 2 pulgadas, paquete de 100 unidades',
                'category_id' => $ferreteria,
                'price' => 5.50,
                'stock' => 2,
                'min_stock' => 50,
                'active' => true,
            ],
            [
                'code' => 'FER-002',
                'name' => 'Clavos 2" (kg)',
                'description' => 'Clavos de 2 pulgadas, precio por kilogramo',
                'category_id' => $ferreteria,
                'price' => 4.25,
                'stock' => 25,
                'min_stock' => 10,
                'active' => true,
            ],
        ];

        foreach ($products as $product) {
            Product::create($product);
        }
    }
}
