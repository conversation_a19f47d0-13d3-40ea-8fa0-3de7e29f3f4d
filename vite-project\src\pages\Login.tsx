"use client"

import type React from "react"
import { useState } from "react"
import { useNavigate } from "react-router-dom"
import { useAuth } from "../contexts/AuthContext"
import { <PERSON>, Wrench, AlertCircle, Loader2 } from "lucide-react"

const Login: React.FC = () => {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [error, setError] = useState("")
  const [isLoading, setIsLoading] = useState(false)

  const { login } = useAuth()
  const navigate = useNavigate()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")
    setIsLoading(true)

    try {
      // Llamada simplificada al login
      const response = await login({ email, password });

      // Obtener el rol directamente de la respuesta
      const userRole = response?.user?.role;

      // Redirigir según el rol
      if (userRole === 'admin') {
        navigate("/admin/dashboard");
      } else {
        navigate("/employee/dashboard");
      }
    } catch (err: any) {
      // Mostrar mensaje de error genérico
      setError("Error al iniciar sesión. Por favor, verifica tus credenciales e inténtalo de nuevo.");
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-zinc-800 to-zinc-900">
      {/* Decorative elements */}
      <div className="absolute top-10 left-10 text-red-500 opacity-20 rotate-12">
        <Hammer size={80} />
      </div>
      <div className="absolute bottom-10 right-10 text-red-500 opacity-20 -rotate-12">
        <Wrench size={80} />
      </div>

      <div className="max-w-md w-full mx-4 relative">
        {/* Card with 3D effect */}
        <div className="absolute inset-0 bg-red-600 rounded-2xl transform rotate-1 translate-x-1 translate-y-1 -z-10"></div>
        <div className="absolute inset-0 bg-zinc-700 rounded-2xl transform -rotate-1 -translate-x-1 -translate-y-1 -z-10"></div>

        <div className="bg-zinc-100 p-8 rounded-2xl shadow-2xl border-t-4 border-red-600">
          {/* Logo and title */}
          <div className="flex items-center justify-center mb-8">
            <div className="bg-red-600 p-3 rounded-full">
              <Wrench className="h-8 w-8 text-white" />
            </div>
            <h1 className="text-3xl font-extrabold ml-3 text-zinc-800">
              FERRETERÍA<span className="text-red-600">PRO</span>
            </h1>
          </div>

          <h2 className="text-xl font-bold text-center text-zinc-700 mb-6">Acceso al Sistema</h2>

          {error && (
            <div className="mb-6 p-4 bg-red-50 border-l-4 border-red-600 text-red-700 rounded-md flex items-start">
              <AlertCircle className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" />
              <span>{error}</span>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-5">
            <div>
              <label htmlFor="email" className="block text-zinc-700 text-sm font-bold mb-2">
                Correo Electrónico
              </label>
              <div className="relative">
                <input
                  type="email"
                  id="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full px-4 py-3 border-2 border-zinc-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all"
                  required
                  placeholder="<EMAIL>"
                />
              </div>
            </div>

            <div>
              <div className="flex items-center justify-between mb-2">
                <label htmlFor="password" className="block text-zinc-700 text-sm font-bold">
                  Contraseña
                </label>
                <a href="#" className="text-sm text-red-600 hover:text-red-800 font-medium">
                  ¿Olvidaste tu contraseña?
                </a>
              </div>
              <div className="relative">
                <input
                  type="password"
                  id="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full px-4 py-3 border-2 border-zinc-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all"
                  required
                  placeholder="••••••••"
                />
              </div>
            </div>

            <div className="pt-2">
              <button
                type="submit"
                disabled={isLoading}
                className={`w-full py-3 px-4 bg-gradient-to-r from-red-600 to-red-700 text-white rounded-lg font-bold text-lg shadow-lg hover:from-red-700 hover:to-red-800 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transform transition-all ${
                  isLoading ? "opacity-70 cursor-not-allowed" : "hover:-translate-y-0.5 active:translate-y-0"
                }`}
              >
                {isLoading ? (
                  <span className="flex items-center justify-center">
                    <Loader2 className="animate-spin mr-2 h-5 w-5" />
                    Iniciando sesión...
                  </span>
                ) : (
                  "Iniciar Sesión"
                )}
              </button>
            </div>
          </form>

          <div className="mt-8 pt-6 border-t border-zinc-200 text-center text-zinc-600 text-sm">
            Sistema de Gestión de Inventario y Ventas
            <div className="mt-1 font-medium">Ferretería Pro © {new Date().getFullYear()}</div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Login
