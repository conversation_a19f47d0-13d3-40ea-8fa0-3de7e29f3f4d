import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import AdminLayout from '../../../components/layouts/AdminLayout';
import {
  BarChart2,
  FileText,
  Download,
  Calendar,
  Filter,
  PieChart,
  TrendingUp,
  Package,
  ShoppingCart,
  CircleDollarSign,
  Users,
  ChevronRight,
  AlertTriangle,
  CheckCircle,
  XCircle
} from 'lucide-react';
import api, { API_ROUTES } from '../../../utils/api';

// Interfaces
interface ReportOption {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  type: 'sales' | 'inventory' | 'purchases' | 'employees';
}

const ReportsPage: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [selectedReport, setSelectedReport] = useState<string | null>(null);
  const [startDate, setStartDate] = useState<string>('');
  const [endDate, setEndDate] = useState<string>('');
  const [generatingReport, setGeneratingReport] = useState<boolean>(false);

  // Opciones de reportes
  const reportOptions: ReportOption[] = [
    {
      id: 'sales-summary',
      title: 'Resumen de Ventas',
      description: 'Genera un informe detallado de las ventas en un período específico.',
      icon: <BarChart2 className="h-6 w-6 text-blue-600" />,
      type: 'sales'
    },
    {
      id: 'inventory-status',
      title: 'Estado de Inventario',
      description: 'Muestra el estado actual del inventario, incluyendo productos con bajo stock.',
      icon: <Package className="h-6 w-6 text-amber-600" />,
      type: 'inventory'
    },
    {
      id: 'purchase-orders',
      title: 'Órdenes de Compra',
      description: 'Detalle de las órdenes de compra a proveedores en un período específico.',
      icon: <ShoppingCart className="h-6 w-6 text-purple-600" />,
      type: 'purchases'
    },
    {
      id: 'sales-by-product',
      title: 'Ventas por Producto',
      description: 'Análisis de ventas desglosado por producto para identificar los más vendidos.',
      icon: <PieChart className="h-6 w-6 text-green-600" />,
      type: 'sales'
    },
    {
      id: 'profit-margin',
      title: 'Margen de Beneficio',
      description: 'Análisis del margen de beneficio por producto y categoría.',
      icon: <TrendingUp className="h-6 w-6 text-red-600" />,
      type: 'sales'
    },
    {
      id: 'employee-performance',
      title: 'Rendimiento de Empleados',
      description: 'Evaluación del rendimiento de los empleados basado en ventas y actividad.',
      icon: <Users className="h-6 w-6 text-indigo-600" />,
      type: 'employees'
    },
  ];

  // Efecto para inicializar fechas
  useEffect(() => {
    // Establecer fecha de fin como hoy
    const today = new Date();
    const endDateStr = today.toISOString().split('T')[0];
    setEndDate(endDateStr);

    // Establecer fecha de inicio como 30 días atrás
    const startDateObj = new Date();
    startDateObj.setDate(today.getDate() - 30);
    const startDateStr = startDateObj.toISOString().split('T')[0];
    setStartDate(startDateStr);
  }, []);

  // Función para generar reporte
  const handleGenerateReport = async () => {
    if (!selectedReport) {
      setError('Por favor, selecciona un tipo de reporte.');
      return;
    }

    if (!startDate || !endDate) {
      setError('Por favor, selecciona un rango de fechas válido.');
      return;
    }

    setGeneratingReport(true);
    setError(null);
    setSuccess(null);

    try {
      // Obtener el token del localStorage
      const token = localStorage.getItem('token');

      // Configurar los headers
      const config = {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      };

      // En un entorno real, aquí haríamos una llamada a la API
      // const response = await axios.post('/api/reports/generate', {
      //   report_type: selectedReport,
      //   start_date: startDate,
      //   end_date: endDate
      // }, config);

      // Simulamos una respuesta exitosa después de un tiempo
      setTimeout(() => {
        setSuccess(`Reporte generado exitosamente. Puedes descargarlo ahora.`);
        setGeneratingReport(false);
      }, 2000);
    } catch (err: any) {
      setError('Error al generar el reporte: ' + (err.response?.data?.message || err.message));
      setGeneratingReport(false);
    }
  };

  // Función para descargar reporte
  const handleDownloadReport = async () => {
    if (!selectedReport || !startDate || !endDate) {
      setError('Por favor, selecciona un reporte y las fechas');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Llamada a la API para generar y descargar el reporte
      const response = await api.get(`/reports/download/${selectedReport}`, {
        params: {
          start_date: startDate,
          end_date: endDate
        },
        responseType: 'blob'
      });

      // Crear un enlace de descarga
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `reporte_${selectedReport}_${startDate}_${endDate}.pdf`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      setSuccess('Reporte descargado exitosamente');
    } catch (err: any) {
      console.error('Error al descargar reporte:', err);
      setError('Error al descargar el reporte. Esta funcionalidad estará disponible próximamente.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <AdminLayout>
      <div className="p-6 max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-800 mb-1">Reportes</h1>
          <p className="text-gray-600">Genera informes detallados para analizar el rendimiento de tu negocio</p>
        </div>

        {/* Messages */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg text-red-700">
            <p className="flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2" />
              {error}
            </p>
          </div>
        )}

        {success && (
          <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg text-green-700">
            <p className="flex items-center">
              <CheckCircle className="h-5 w-5 mr-2" />
              {success}
            </p>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Report Options */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
              <div className="p-6 border-b border-gray-100">
                <h2 className="text-lg font-bold text-gray-800">Tipos de Reportes</h2>
              </div>
              <div className="p-6 space-y-4">
                {reportOptions.map((option) => (
                  <div
                    key={option.id}
                    onClick={() => setSelectedReport(option.id)}
                    className={`p-4 rounded-lg cursor-pointer transition-colors ${
                      selectedReport === option.id
                        ? 'bg-orange-50 border border-orange-200'
                        : 'bg-gray-50 border border-gray-200 hover:bg-gray-100'
                    }`}
                  >
                    <div className="flex items-start">
                      <div className="flex-shrink-0 mt-1">
                        {option.icon}
                      </div>
                      <div className="ml-4">
                        <h3 className="text-md font-medium text-gray-900">{option.title}</h3>
                        <p className="mt-1 text-sm text-gray-500">{option.description}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right Column - Report Configuration */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
              <div className="p-6 border-b border-gray-100">
                <h2 className="text-lg font-bold text-gray-800">Configuración del Reporte</h2>
              </div>
              <div className="p-6">
                {selectedReport ? (
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-md font-medium text-gray-900 mb-2">
                        {reportOptions.find(option => option.id === selectedReport)?.title}
                      </h3>
                      <p className="text-sm text-gray-500">
                        {reportOptions.find(option => option.id === selectedReport)?.description}
                      </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label htmlFor="start-date" className="block text-sm font-medium text-gray-700 mb-1">
                          Fecha de Inicio
                        </label>
                        <div className="relative">
                          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <Calendar className="h-5 w-5 text-gray-400" />
                          </div>
                          <input
                            type="date"
                            id="start-date"
                            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                            value={startDate}
                            onChange={(e) => setStartDate(e.target.value)}
                          />
                        </div>
                      </div>
                      <div>
                        <label htmlFor="end-date" className="block text-sm font-medium text-gray-700 mb-1">
                          Fecha de Fin
                        </label>
                        <div className="relative">
                          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <Calendar className="h-5 w-5 text-gray-400" />
                          </div>
                          <input
                            type="date"
                            id="end-date"
                            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                            value={endDate}
                            onChange={(e) => setEndDate(e.target.value)}
                          />
                        </div>
                      </div>
                    </div>

                    <div className="pt-4 border-t border-gray-200">
                      <h3 className="text-md font-medium text-gray-900 mb-2">Opciones Adicionales</h3>
                      <div className="space-y-2">
                        <div className="flex items-center">
                          <input
                            id="include-charts"
                            type="checkbox"
                            className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                            defaultChecked
                          />
                          <label htmlFor="include-charts" className="ml-2 block text-sm text-gray-700">
                            Incluir gráficos
                          </label>
                        </div>
                        <div className="flex items-center">
                          <input
                            id="include-summary"
                            type="checkbox"
                            className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                            defaultChecked
                          />
                          <label htmlFor="include-summary" className="ml-2 block text-sm text-gray-700">
                            Incluir resumen ejecutivo
                          </label>
                        </div>
                      </div>
                    </div>

                    <div className="flex justify-end space-x-3 pt-4">
                      {success ? (
                        <button
                          onClick={handleDownloadReport}
                          className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors"
                        >
                          <Download className="h-5 w-5 mr-2" />
                          Descargar Reporte
                        </button>
                      ) : (
                        <button
                          onClick={handleGenerateReport}
                          disabled={generatingReport}
                          className={`inline-flex items-center px-4 py-2 ${
                            generatingReport
                              ? 'bg-gray-400 cursor-not-allowed'
                              : 'bg-orange-600 hover:bg-orange-700'
                          } text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 transition-colors`}
                        >
                          {generatingReport ? (
                            <>
                              <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              Generando...
                            </>
                          ) : (
                            <>
                              <FileText className="h-5 w-5 mr-2" />
                              Generar Reporte
                            </>
                          )}
                        </button>
                      )}
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <BarChart2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Selecciona un Tipo de Reporte</h3>
                    <p className="text-sm text-gray-500 max-w-md mx-auto">
                      Elige uno de los tipos de reportes disponibles en el panel izquierdo para comenzar a configurar tu informe.
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Recent Reports */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden mt-6">
              <div className="p-6 border-b border-gray-100">
                <h2 className="text-lg font-bold text-gray-800">Reportes Recientes</h2>
              </div>
              <div className="divide-y divide-gray-200">
                <div className="p-4 hover:bg-gray-50">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <BarChart2 className="h-5 w-5 text-blue-600 mr-3" />
                      <div>
                        <h3 className="text-sm font-medium text-gray-900">Resumen de Ventas</h3>
                        <p className="text-xs text-gray-500">01/05/2023 - 31/05/2023</p>
                      </div>
                    </div>
                    <button className="text-sm text-orange-600 hover:text-orange-700 flex items-center">
                      Descargar <Download className="h-4 w-4 ml-1" />
                    </button>
                  </div>
                </div>
                <div className="p-4 hover:bg-gray-50">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <Package className="h-5 w-5 text-amber-600 mr-3" />
                      <div>
                        <h3 className="text-sm font-medium text-gray-900">Estado de Inventario</h3>
                        <p className="text-xs text-gray-500">15/05/2023</p>
                      </div>
                    </div>
                    <button className="text-sm text-orange-600 hover:text-orange-700 flex items-center">
                      Descargar <Download className="h-4 w-4 ml-1" />
                    </button>
                  </div>
                </div>
                <div className="p-4 hover:bg-gray-50">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <PieChart className="h-5 w-5 text-green-600 mr-3" />
                      <div>
                        <h3 className="text-sm font-medium text-gray-900">Ventas por Producto</h3>
                        <p className="text-xs text-gray-500">01/04/2023 - 30/04/2023</p>
                      </div>
                    </div>
                    <button className="text-sm text-orange-600 hover:text-orange-700 flex items-center">
                      Descargar <Download className="h-4 w-4 ml-1" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
};

export default ReportsPage;
