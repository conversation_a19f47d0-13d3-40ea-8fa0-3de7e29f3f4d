import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import AdminLayout from '../../../components/layouts/AdminLayout';
import axios from 'axios';
import { User } from '../../../contexts/AuthContext';
import {
  Save,
  ArrowLeft,
  Search,
  User as UserIcon,
  Mail,
  Phone,
  CreditCard,
  Lock,
  CheckCircle,
  AlertTriangle,
  Loader
} from 'lucide-react';

// Definir la interfaz EmployeeFormData
interface EmployeeFormData {
  name: string;
  email: string;
  phone?: string;
  dni?: string;
  password?: string;
}

// Interfaz para la respuesta de la API de DNI
interface DniResponse {
  success: boolean;
  dni?: string;
  nombre?: string;
  nombres?: string;
  apellidos?: string;
  message?: string;
}

const EmployeeForm: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const isEditMode = !!id;
  const navigate = useNavigate();

  const [formData, setFormData] = useState<EmployeeFormData>({
    name: '',
    email: '',
    phone: '',
    dni: '',
    password: '',
  });

  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isFetching, setIsFetching] = useState(isEditMode);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [isSearchingDni, setIsSearchingDni] = useState(false);

  useEffect(() => {
    const fetchEmployee = async () => {
      if (isEditMode && id) {
        try {
          // Obtener el token del localStorage
          const token = localStorage.getItem('token');

          // Configurar los headers
          const config = {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          };

          // Hacer la petición directamente con axios
          const response = await axios.get(`/api/employees/${id}`, config);
          const employee = response.data;

          setFormData({
            name: employee.name,
            email: employee.email,
            phone: employee.phone || '',
            dni: employee.dni || '',
            password: '', // No mostrar la contraseña actual
          });
          setError('');
        } catch (err: any) {
          setError('Error al cargar datos del empleado: ' + (err.response?.data?.message || err.message));
        } finally {
          setIsFetching(false);
        }
      } else {
        setIsFetching(false);
      }
    };

    fetchEmployee();
  }, [id, isEditMode]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Limpiar mensajes
    setError('');
    setSuccess('');
  };

  // Función para consultar DNI usando un proxy CORS
  const handleSearchDni = async () => {
    if (!formData.dni || formData.dni.length !== 8) {
      setError('El DNI debe tener 8 dígitos');
      return;
    }

    setIsSearchingDni(true);
    setError('');
    setSuccess('');

    // Primero, verificar si ya tenemos este DNI en localStorage
    const existingData = JSON.parse(localStorage.getItem('dniConsults') || '[]');
    const dniData = existingData.find((item: any) => item.dni === formData.dni);

    if (dniData) {
      // Si ya tenemos los datos en localStorage, usarlos
      setFormData(prev => ({
        ...prev,
        name: dniData.fullName,
        // Usar el correo existente o generar uno nuevo
        email: prev.email || `${dniData.nombres.toLowerCase().replace(/\s+/g, '.')}.${dniData.apellidos.split(' ')[0].toLowerCase()}@ferreteriapro.com`
      }));

      setSuccess('Datos obtenidos del almacenamiento local');
      setIsSearchingDni(false);
      return;
    }

    try {
      // Hacer la solicitud a través del proxy configurado en vite.config.ts
      const response = await axios.get(`/reniec-api/api/customer/consultar-dni/${formData.dni}`);

      if (!response.data || !response.data.success) {
        throw new Error('No se pudo obtener información del DNI');
      }

      // Extraer los datos de la respuesta
      const { nombres, apellidos, dni } = response.data;

        // Crear el nombre completo
        const fullName = `${nombres} ${apellidos}`.trim();

        // Actualizar el formulario
        setFormData(prev => ({
          ...prev,
          name: fullName,
          // Generar un correo electrónico sugerido basado en el nombre
          email: prev.email || `${nombres.toLowerCase().replace(/\s+/g, '.')}.${apellidos.split(' ')[0].toLowerCase()}@ferreteriapro.com`
        }));

        setSuccess(`Datos obtenidos correctamente de RENIEC`);

        // Guardar los datos en localStorage para futuras consultas
        const newDniData = {
          dni,
          nombres,
          apellidos,
          fullName,
          timestamp: new Date().toISOString()
        };

        // Verificar si este DNI ya existe
        const existingIndex = existingData.findIndex((item: any) => item.dni === dni);

        if (existingIndex >= 0) {
          // Actualizar datos existentes
          existingData[existingIndex] = newDniData;
        } else {
          // Agregar nuevos datos
          existingData.push(newDniData);
        }

        // Guardar en localStorage
        localStorage.setItem('dniConsults', JSON.stringify(existingData));
    } catch (err: any) {
      console.error('Error al consultar DNI:', err);

      // Si ya verificamos localStorage al inicio y no encontramos datos,
      // simplemente mostramos el error
      setError('Error al consultar DNI: ' + (err.response?.data?.message || err.message));
    } finally {
      setIsSearchingDni(false);
    }
  };

  const validateForm = () => {
    if (!formData.name || !formData.email) {
      setError('Nombre y correo electrónico son obligatorios');
      return false;
    }

    if (!isEditMode && !formData.password) {
      setError('La contraseña es obligatoria para nuevos empleados');
      return false;
    }

    if (formData.password && formData.password !== confirmPassword) {
      setError('Las contraseñas no coinciden');
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // Obtener el token del localStorage
      const token = localStorage.getItem('token');

      // Configurar los headers
      const config = {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      };

      if (isEditMode && id) {
        // Si no se proporciona contraseña en modo edición, no la enviamos
        const dataToSend = { ...formData };
        if (!dataToSend.password) {
          delete dataToSend.password;
        }

        // Hacer la petición directamente con axios
        await axios.put(`/api/employees/${id}`, dataToSend, config);
      } else {
        // Hacer la petición directamente con axios
        await axios.post('/api/employees', formData, config);
      }

      navigate('/admin/employees');
    } catch (err: any) {
      setError('Error al guardar empleado: ' + (err.response?.data?.message || err.message));
    } finally {
      setIsLoading(false);
    }
  };

  if (isFetching) {
    return (
      <AdminLayout>
        <div className="p-6 flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500"></div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="p-6 max-w-7xl mx-auto">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-800 mb-1">
              {isEditMode ? 'Editar Empleado' : 'Crear Nuevo Empleado'}
            </h1>
            <p className="text-gray-600">
              {isEditMode
                ? 'Actualiza la información del empleado'
                : 'Completa el formulario para registrar un nuevo empleado'}
            </p>
          </div>
          <div className="mt-4 md:mt-0">
            <button
              onClick={() => navigate('/admin/employees')}
              className="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors"
            >
              <ArrowLeft className="h-5 w-5 mr-2" /> Volver
            </button>
          </div>
        </div>

        {/* Error message */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg text-red-700">
            <p className="flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2" />
              {error}
            </p>
          </div>
        )}

        {/* Success message */}
        {success && (
          <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg text-green-700">
            <p className="flex items-center">
              <CheckCircle className="h-5 w-5 mr-2" />
              {success}
            </p>
          </div>
        )}

        <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
          <div className="p-6 border-b border-gray-100">
            <h2 className="text-lg font-bold text-gray-800">Información del Empleado</h2>
          </div>
          <div className="p-6">
            <form onSubmit={handleSubmit}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* DNI con búsqueda */}
                <div>
                  <label htmlFor="dni" className="block text-sm font-medium text-gray-700 mb-1">
                    DNI
                  </label>
                  <div className="relative flex">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <CreditCard className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      type="text"
                      id="dni"
                      name="dni"
                      value={formData.dni}
                      onChange={handleChange}
                      className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                      placeholder="Ingrese el DNI"
                      maxLength={8}
                    />
                    <button
                      type="button"
                      onClick={handleSearchDni}
                      disabled={isSearchingDni || !formData.dni || formData.dni.length !== 8}
                      className={`ml-2 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md ${
                        isSearchingDni || !formData.dni || formData.dni.length !== 8
                          ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                          : 'bg-orange-600 text-white hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500'
                      }`}
                    >
                      {isSearchingDni ? (
                        <Loader className="h-4 w-4 animate-spin" />
                      ) : (
                        <Search className="h-4 w-4" />
                      )}
                      <span className="ml-1">Buscar</span>
                    </button>
                  </div>
                  <p className="mt-1 text-xs text-gray-500">
                    Ingrese el DNI y haga clic en buscar para obtener los datos de RENIEC
                  </p>
                </div>

                {/* Nombre completo */}
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                    Nombre Completo *
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <UserIcon className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                      placeholder="Nombre completo del empleado"
                      required
                    />
                  </div>
                </div>

                {/* Correo electrónico */}
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                    Correo Electrónico *
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Mail className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>
                </div>

                {/* Teléfono */}
                <div>
                  <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                    Teléfono
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Phone className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      type="text"
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleChange}
                      className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                      placeholder="Número de teléfono"
                    />
                  </div>
                </div>

                {/* Contraseña */}
                <div>
                  <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                    {isEditMode ? 'Nueva Contraseña (dejar en blanco para mantener la actual)' : 'Contraseña *'}
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Lock className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      type="password"
                      id="password"
                      name="password"
                      value={formData.password}
                      onChange={handleChange}
                      className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                      placeholder="Contraseña"
                      required={!isEditMode}
                    />
                  </div>
                </div>

                {/* Confirmar contraseña */}
                <div>
                  <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
                    Confirmar Contraseña
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Lock className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      type="password"
                      id="confirmPassword"
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                      className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                      placeholder="Confirmar contraseña"
                      required={!isEditMode || !!formData.password}
                    />
                  </div>
                </div>
              </div>

              <div className="mt-6 flex justify-end">
                <button
                  type="submit"
                  disabled={isLoading}
                  className={`inline-flex items-center px-4 py-2 ${
                    isLoading
                      ? 'bg-gray-400 cursor-not-allowed'
                      : 'bg-orange-600 hover:bg-orange-700'
                  } text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 transition-colors`}
                >
                  {isLoading ? (
                    <>
                      <Loader className="animate-spin h-5 w-5 mr-2" />
                      Guardando...
                    </>
                  ) : (
                    <>
                      <Save className="h-5 w-5 mr-2" />
                      Guardar Empleado
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
};

export default EmployeeForm;
