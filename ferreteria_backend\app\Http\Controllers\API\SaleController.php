<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\CashRegister;
use App\Models\Customer;
use App\Models\Product;
use App\Models\Sale;
use App\Models\SaleItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class SaleController extends Controller
{
    /**
     * Obtener todas las ventas.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $sales = Sale::with(['user', 'customer', 'items.product'])->orderBy('created_at', 'desc')->get();
        
        return response()->json([
            'success' => true,
            'data' => $sales
        ]);
    }

    /**
     * Almacenar una nueva venta.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // Verificar si hay una caja abierta
        $cashRegister = CashRegister::where('status', 'open')->first();
        
        if (!$cashRegister) {
            return response()->json([
                'success' => false,
                'message' => 'No hay ninguna caja abierta. Debe abrir una caja antes de realizar una venta.'
            ], 422);
        }

        $validator = Validator::make($request->all(), [
            'customer_id' => 'required|exists:customers,id',
            'document_type' => 'required|in:boleta,factura,ticket',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.unit_price' => 'required|numeric|min:0',
            'notes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        // Verificar si el cliente existe
        $customer = Customer::find($request->input('customer_id'));
        
        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'Cliente no encontrado'
            ], 404);
        }

        // Verificar si el tipo de documento es válido para el tipo de cliente
        if ($request->input('document_type') === 'factura' && $customer->document_type !== 'ruc') {
            return response()->json([
                'success' => false,
                'message' => 'Solo se puede emitir facturas a clientes con RUC'
            ], 422);
        }

        // Verificar stock de productos
        $items = $request->input('items');
        foreach ($items as $item) {
            $product = Product::find($item['product_id']);
            
            if (!$product) {
                return response()->json([
                    'success' => false,
                    'message' => 'Producto no encontrado: ID ' . $item['product_id']
                ], 404);
            }
            
            if ($product->stock < $item['quantity']) {
                return response()->json([
                    'success' => false,
                    'message' => 'Stock insuficiente para el producto: ' . $product->name
                ], 422);
            }
        }

        // Calcular total
        $total = 0;
        foreach ($items as $item) {
            $total += $item['quantity'] * $item['unit_price'];
        }

        // Calcular impuesto (IGV 18%)
        $tax = $request->input('document_type') === 'factura' ? $total * 0.18 : 0;

        try {
            DB::beginTransaction();

            // Crear la venta
            $sale = Sale::create([
                'user_id' => Auth::id(),
                'customer_id' => $request->input('customer_id'),
                'cash_register_id' => $cashRegister->id,
                'document_type' => $request->input('document_type'),
                'total' => $total,
                'tax' => $tax,
                'status' => 'completed',
                'notes' => $request->input('notes'),
            ]);

            // Generar número de documento
            $sale->generateDocumentNumber();

            // Crear los items de la venta
            foreach ($items as $item) {
                $product = Product::find($item['product_id']);
                
                // Crear el item
                SaleItem::create([
                    'sale_id' => $sale->id,
                    'product_id' => $item['product_id'],
                    'quantity' => $item['quantity'],
                    'unit_price' => $item['unit_price'],
                    'subtotal' => $item['quantity'] * $item['unit_price'],
                ]);
                
                // Actualizar el stock del producto
                $product->updateStock($item['quantity']);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Venta creada exitosamente',
                'data' => $sale->load(['user', 'customer', 'items.product'])
            ], 201);
        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'success' => false,
                'message' => 'Error al crear la venta: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Obtener una venta específica.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $sale = Sale::with(['user', 'customer', 'items.product'])->find($id);

        if (!$sale) {
            return response()->json([
                'success' => false,
                'message' => 'Venta no encontrada'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $sale
        ]);
    }

    /**
     * Cancelar una venta.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function cancel($id)
    {
        $sale = Sale::find($id);

        if (!$sale) {
            return response()->json([
                'success' => false,
                'message' => 'Venta no encontrada'
            ], 404);
        }

        if ($sale->status === 'cancelled') {
            return response()->json([
                'success' => false,
                'message' => 'Esta venta ya está cancelada'
            ], 422);
        }

        try {
            DB::beginTransaction();

            // Actualizar el estado de la venta
            $sale->status = 'cancelled';
            $sale->save();

            // Devolver los productos al inventario
            foreach ($sale->items as $item) {
                $product = $item->product;
                $product->stock += $item->quantity;
                $product->save();
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Venta cancelada exitosamente',
                'data' => $sale
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'success' => false,
                'message' => 'Error al cancelar la venta: ' . $e->getMessage()
            ], 500);
        }
    }
}
