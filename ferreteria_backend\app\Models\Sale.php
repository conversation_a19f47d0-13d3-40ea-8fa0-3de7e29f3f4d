<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Sale extends Model
{
    use HasFactory;

    /**
     * Los atributos que son asignables en masa.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'customer_id',
        'cash_register_id',
        'document_type',
        'document_number',
        'total',
        'tax',
        'status',
        'notes',
    ];

    /**
     * Los atributos que deben ser convertidos a tipos nativos.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'total' => 'decimal:2',
        'tax' => 'decimal:2',
    ];

    /**
     * Obtener el usuario que realizó la venta.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Obtener el cliente asociado a la venta.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Obtener la caja asociada a la venta.
     */
    public function cashRegister(): BelongsTo
    {
        return $this->belongsTo(CashRegister::class);
    }

    /**
     * Obtener los items de la venta.
     */
    public function items(): HasMany
    {
        return $this->hasMany(SaleItem::class);
    }

    /**
     * Verificar si la venta está completada.
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Verificar si la venta está cancelada.
     */
    public function isCancelled(): bool
    {
        return $this->status === 'cancelled';
    }

    /**
     * Generar número de documento.
     */
    public function generateDocumentNumber(): void
    {
        $prefix = '';
        
        switch ($this->document_type) {
            case 'boleta':
                $prefix = 'B';
                break;
            case 'factura':
                $prefix = 'F';
                break;
            case 'ticket':
                $prefix = 'T';
                break;
        }
        
        $lastSale = self::where('document_type', $this->document_type)
            ->orderBy('id', 'desc')
            ->first();
        
        $number = $lastSale ? (int)substr($lastSale->document_number, 1) + 1 : 1;
        
        $this->document_number = $prefix . str_pad($number, 8, '0', STR_PAD_LEFT);
        $this->save();
    }
}
