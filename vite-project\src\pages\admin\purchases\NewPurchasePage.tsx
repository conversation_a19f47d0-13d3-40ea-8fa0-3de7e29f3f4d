import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import AdminLayout from '../../../components/layouts/AdminLayout';
import axios from 'axios';
import {
  ShoppingCart,
  Search,
  Plus,
  Trash2,
  ArrowLeft,
  CreditCard,
  Building2,
  Receipt,
  Calendar,
  Clock,
  Save,
  AlertTriangle,
  Loader
} from 'lucide-react';

// Interfaces
interface Product {
  id: number;
  name: string;
  price: number;
  stock: number;
  code: string;
}

interface Customer {
  id?: number;
  documentType: 'dni' | 'ruc';
  documentNumber: string;
  name: string;
  email?: string;
  phone?: string;
}

interface PurchaseItem {
  productId: number;
  productName: string;
  quantity: number;
  unitPrice: number;
  subtotal: number;
}

interface Purchase {
  customer: Customer;
  documentType: 'boleta' | 'factura' | 'ticket';
  items: PurchaseItem[];
  total: number;
  date: string;
  time: string;
}

const NewPurchasePage: React.FC = () => {
  const navigate = useNavigate();

  // Estados
  const [documentType, setDocumentType] = useState<'dni' | 'ruc'>('dni');
  const [documentNumber, setDocumentNumber] = useState<string>('');
  const [isSearchingDocument, setIsSearchingDocument] = useState<boolean>(false);
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [purchaseType, setPurchaseType] = useState<'boleta' | 'factura' | 'ticket'>('boleta');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [selectedProducts, setSelectedProducts] = useState<PurchaseItem[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Fecha y hora actual
  const currentDate = new Date().toLocaleDateString('es-ES');
  const currentTime = new Date().toLocaleTimeString('es-ES', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });

  // Productos (se cargarán desde el backend)

  // Cargar productos
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        const token = localStorage.getItem('token');

        const response = await axios.get('/api/products', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (response.data.success) {
          setProducts(response.data.data);
          setFilteredProducts(response.data.data);
        } else {
          setError('Error al cargar productos');
        }
      } catch (err: any) {
        console.error('Error al cargar productos:', err);
        setError('Error al cargar productos: ' + (err.response?.data?.message || err.message));
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, []);

  // Filtrar productos cuando cambia el término de búsqueda
  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredProducts(products);
    } else {
      const filtered = products.filter(product =>
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.code.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredProducts(filtered);
    }
  }, [searchTerm, products]);

  // Calcular total
  const total = selectedProducts.reduce((sum, item) => sum + item.subtotal, 0);

  // Función para consultar documento (DNI o RUC)
  const handleSearchDocument = async () => {
    if (!documentNumber) {
      setError('Por favor, ingrese un número de documento');
      return;
    }

    if (documentType === 'dni' && documentNumber.length !== 8) {
      setError('El DNI debe tener 8 dígitos');
      return;
    }

    if (documentType === 'ruc' && documentNumber.length !== 11) {
      setError('El RUC debe tener 11 dígitos');
      return;
    }

    setIsSearchingDocument(true);
    setError(null);
    setSuccess(null);

    try {
      if (documentType === 'dni') {
        // Verificar si ya tenemos este DNI en localStorage
        const storedData = localStorage.getItem('dniConsults');
        const dniConsults = storedData ? JSON.parse(storedData) : [];
        const existingData = dniConsults.find((item: any) => item.dni === documentNumber);

        if (existingData) {
          // Si ya tenemos los datos en localStorage, usarlos
          setCustomer({
            documentType: 'dni',
            documentNumber: documentNumber,
            name: existingData.fullName,
            email: '',
            phone: ''
          });
          setSuccess('Datos obtenidos del almacenamiento local');
        } else {
          // Hacer la solicitud a través del proxy configurado en vite.config.ts
          const response = await axios.get(`/reniec-api/api/customer/consultar-dni/${documentNumber}`);

          if (response.data && response.data.success) {
            // Extraer los datos de la respuesta
            const { nombres, apellidos, dni } = response.data;

            // Crear el nombre completo
            const fullName = `${nombres} ${apellidos}`.trim();

            // Actualizar el cliente
            setCustomer({
              documentType: 'dni',
              documentNumber: dni,
              name: fullName,
              email: '',
              phone: ''
            });

            setSuccess('Datos obtenidos correctamente de RENIEC');

            // Guardar los datos en localStorage para futuras consultas
            const newDniData = {
              dni,
              nombres,
              apellidos,
              fullName,
              timestamp: new Date().toISOString()
            };

            // Verificar si este DNI ya existe
            const existingIndex = dniConsults.findIndex((item: any) => item.dni === dni);

            if (existingIndex >= 0) {
              // Actualizar datos existentes
              dniConsults[existingIndex] = newDniData;
            } else {
              // Agregar nuevos datos
              dniConsults.push(newDniData);
            }

            // Guardar en localStorage
            localStorage.setItem('dniConsults', JSON.stringify(dniConsults));
          } else {
            throw new Error('No se pudo obtener información del DNI');
          }
        }
      } else {
        // Simulamos consulta de RUC (en un entorno real, se consultaría a SUNAT)
        setTimeout(() => {
          setCustomer({
            documentType: 'ruc',
            documentNumber: documentNumber,
            name: `EMPRESA CON RUC ${documentNumber}`,
            email: `empresa${documentNumber}@example.com`,
            phone: ''
          });
          setSuccess('Datos de empresa obtenidos correctamente');
        }, 1000);
      }
    } catch (err: any) {
      console.error('Error al consultar documento:', err);
      setError('Error al consultar documento: ' + (err.response?.data?.message || err.message));
    } finally {
      setIsSearchingDocument(false);
    }
  };

  // Función para agregar un producto a la compra
  const handleAddProduct = (product: Product) => {
    // Verificar si el producto ya está en la lista
    const existingIndex = selectedProducts.findIndex(item => item.productId === product.id);

    if (existingIndex >= 0) {
      // Si ya existe, incrementar la cantidad
      const updatedProducts = [...selectedProducts];
      updatedProducts[existingIndex].quantity += 1;
      updatedProducts[existingIndex].subtotal = updatedProducts[existingIndex].quantity * updatedProducts[existingIndex].unitPrice;
      setSelectedProducts(updatedProducts);
    } else {
      // Si no existe, agregarlo a la lista
      setSelectedProducts([
        ...selectedProducts,
        {
          productId: product.id,
          productName: product.name,
          quantity: 1,
          unitPrice: product.price,
          subtotal: product.price
        }
      ]);
    }

    // Limpiar el término de búsqueda
    setSearchTerm('');
  };

  // Función para actualizar la cantidad de un producto
  const handleUpdateQuantity = (index: number, quantity: number) => {
    if (quantity <= 0) return;

    const updatedProducts = [...selectedProducts];
    updatedProducts[index].quantity = quantity;
    updatedProducts[index].subtotal = quantity * updatedProducts[index].unitPrice;
    setSelectedProducts(updatedProducts);
  };

  // Función para eliminar un producto de la compra
  const handleRemoveProduct = (index: number) => {
    setSelectedProducts(selectedProducts.filter((_, i) => i !== index));
  };

  // Función para guardar la compra
  const handleSavePurchase = async () => {
    if (!customer) {
      setError('Por favor, consulte un documento para identificar al cliente');
      return;
    }

    if (selectedProducts.length === 0) {
      setError('Por favor, agregue al menos un producto a la compra');
      return;
    }

    try {
      const token = localStorage.getItem('token');

      // Crear objeto de compra para enviar al backend
      const saleData = {
        customer_id: customer.id || null,
        document_type: purchaseType,
        items: selectedProducts.map(item => ({
          product_id: item.productId,
          quantity: item.quantity,
          unit_price: item.unitPrice
        })),
        notes: ''
      };

      // Enviar la compra al backend
      const response = await axios.post('/api/sales', saleData, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.data.success) {
        setSuccess('Compra guardada correctamente');

        // Limpiar el formulario después de guardar
        setTimeout(() => {
          setCustomer(null);
          setDocumentNumber('');
          setSelectedProducts([]);
          setPurchaseType('boleta');
          navigate('/admin/purchases');
        }, 2000);
      } else {
        setError('Error al guardar la compra: ' + response.data.message);
      }
    } catch (err: any) {
      console.error('Error al guardar la compra:', err);
      setError('Error al guardar la compra: ' + (err.response?.data?.message || err.message));
    }
  };

  return (
    <AdminLayout>
      <div className="p-6 max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-800 mb-1">Nueva Compra</h1>
            <p className="text-gray-600">Registra una nueva venta y actualiza el inventario</p>
          </div>
          <div className="mt-4 md:mt-0 flex space-x-3">
            <button
              onClick={() => navigate('/admin/purchases')}
              className="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors"
            >
              <ArrowLeft className="h-5 w-5 mr-2" /> Volver
            </button>
          </div>
        </div>

        {/* Error and Success Messages */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg text-red-700">
            <p className="flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2" />
              {error}
            </p>
          </div>
        )}

        {success && (
          <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg text-green-700">
            <p className="flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2" />
              {success}
            </p>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Customer and Document Info */}
          <div className="lg:col-span-1 space-y-6">
            {/* Document Search */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
              <h2 className="text-lg font-bold text-gray-800 mb-4">Datos del Cliente</h2>

              <div className="space-y-4">
                {/* Document Type Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Tipo de Documento
                  </label>
                  <div className="flex space-x-4">
                    <label className="inline-flex items-center">
                      <input
                        type="radio"
                        className="form-radio text-orange-600"
                        name="documentType"
                        value="dni"
                        checked={documentType === 'dni'}
                        onChange={() => setDocumentType('dni')}
                      />
                      <span className="ml-2">DNI</span>
                    </label>
                    <label className="inline-flex items-center">
                      <input
                        type="radio"
                        className="form-radio text-orange-600"
                        name="documentType"
                        value="ruc"
                        checked={documentType === 'ruc'}
                        onChange={() => setDocumentType('ruc')}
                      />
                      <span className="ml-2">RUC</span>
                    </label>
                  </div>
                </div>

                {/* Document Number Input */}
                <div>
                  <label htmlFor="documentNumber" className="block text-sm font-medium text-gray-700 mb-1">
                    Número de {documentType.toUpperCase()}
                  </label>
                  <div className="relative flex">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      {documentType === 'dni' ? (
                        <CreditCard className="h-5 w-5 text-gray-400" />
                      ) : (
                        <Building2 className="h-5 w-5 text-gray-400" />
                      )}
                    </div>
                    <input
                      type="text"
                      id="documentNumber"
                      value={documentNumber}
                      onChange={(e) => setDocumentNumber(e.target.value.replace(/\D/g, ''))}
                      className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                      placeholder={`Ingrese el ${documentType.toUpperCase()}`}
                      maxLength={documentType === 'dni' ? 8 : 11}
                    />
                    <button
                      type="button"
                      onClick={handleSearchDocument}
                      disabled={isSearchingDocument || !documentNumber || (documentType === 'dni' && documentNumber.length !== 8) || (documentType === 'ruc' && documentNumber.length !== 11)}
                      className={`ml-2 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md ${
                        isSearchingDocument || !documentNumber || (documentType === 'dni' && documentNumber.length !== 8) || (documentType === 'ruc' && documentNumber.length !== 11)
                          ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                          : 'bg-orange-600 text-white hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500'
                      }`}
                    >
                      {isSearchingDocument ? (
                        <Loader className="h-4 w-4 animate-spin" />
                      ) : (
                        <Search className="h-4 w-4" />
                      )}
                      <span className="ml-1">Buscar</span>
                    </button>
                  </div>
                </div>

                {/* Customer Info */}
                {customer && (
                  <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                    <h3 className="font-medium text-gray-800 mb-2">Información del {documentType === 'dni' ? 'Cliente' : 'Empresa'}</h3>
                    <p className="text-sm text-gray-600"><span className="font-medium">Nombre:</span> {customer.name}</p>
                    {customer.email && (
                      <p className="text-sm text-gray-600"><span className="font-medium">Email:</span> {customer.email}</p>
                    )}
                  </div>
                )}
              </div>
            </div>

            {/* Document Type */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
              <h2 className="text-lg font-bold text-gray-800 mb-4">Tipo de Comprobante</h2>

              <div className="space-y-2">
                <label className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
                  <input
                    type="radio"
                    className="form-radio text-orange-600"
                    name="purchaseType"
                    value="boleta"
                    checked={purchaseType === 'boleta'}
                    onChange={() => setPurchaseType('boleta')}
                  />
                  <span className="ml-2 flex items-center">
                    <Receipt className="h-5 w-5 text-gray-500 mr-2" />
                    Boleta
                  </span>
                </label>

                <label className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
                  <input
                    type="radio"
                    className="form-radio text-orange-600"
                    name="purchaseType"
                    value="factura"
                    checked={purchaseType === 'factura'}
                    onChange={() => setPurchaseType('factura')}
                  />
                  <span className="ml-2 flex items-center">
                    <Receipt className="h-5 w-5 text-gray-500 mr-2" />
                    Factura
                  </span>
                </label>

                <label className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
                  <input
                    type="radio"
                    className="form-radio text-orange-600"
                    name="purchaseType"
                    value="ticket"
                    checked={purchaseType === 'ticket'}
                    onChange={() => setPurchaseType('ticket')}
                  />
                  <span className="ml-2 flex items-center">
                    <Receipt className="h-5 w-5 text-gray-500 mr-2" />
                    Ticket de Control
                  </span>
                </label>
              </div>
            </div>

            {/* Date and Time */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
              <h2 className="text-lg font-bold text-gray-800 mb-4">Fecha y Hora</h2>

              <div className="space-y-4">
                <div className="flex items-center">
                  <Calendar className="h-5 w-5 text-gray-500 mr-2" />
                  <span className="text-gray-700">{currentDate}</span>
                </div>

                <div className="flex items-center">
                  <Clock className="h-5 w-5 text-gray-500 mr-2" />
                  <span className="text-gray-700">{currentTime}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Products and Total */}
          <div className="lg:col-span-2 space-y-6">
            {/* Product Search */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
              <h2 className="text-lg font-bold text-gray-800 mb-4">Buscar Productos</h2>

              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                  placeholder="Buscar por nombre o código..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>

              {searchTerm && (
                <div className="mt-4 max-h-60 overflow-y-auto">
                  {loading ? (
                    <div className="p-4 flex justify-center">
                      <Loader className="h-6 w-6 text-orange-600 animate-spin" />
                    </div>
                  ) : filteredProducts.length === 0 ? (
                    <p className="text-gray-500 text-center p-4">No se encontraron productos</p>
                  ) : (
                    <ul className="divide-y divide-gray-200">
                      {filteredProducts.map(product => (
                        <li key={product.id} className="py-2">
                          <button
                            onClick={() => handleAddProduct(product)}
                            className="w-full text-left p-2 hover:bg-gray-50 rounded-lg flex justify-between items-center"
                          >
                            <div>
                              <p className="font-medium text-gray-800">{product.name}</p>
                              <p className="text-sm text-gray-500">Código: {product.code}</p>
                            </div>
                            <div className="text-right">
                              <p className="font-medium text-gray-800">${product.price.toFixed(2)}</p>
                              <p className="text-sm text-gray-500">Stock: {product.stock}</p>
                            </div>
                          </button>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              )}
            </div>

            {/* Selected Products */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
              <h2 className="text-lg font-bold text-gray-800 mb-4">Productos Seleccionados</h2>

              {selectedProducts.length === 0 ? (
                <p className="text-gray-500 text-center p-4">No hay productos seleccionados</p>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="bg-gray-50">
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Producto
                        </th>
                        <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Cantidad
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Precio Unit.
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Subtotal
                        </th>
                        <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Acciones
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {selectedProducts.map((item, index) => (
                        <tr key={index} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800">
                            {item.productName}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-center">
                            <div className="flex items-center justify-center">
                              <button
                                onClick={() => handleUpdateQuantity(index, item.quantity - 1)}
                                className="p-1 rounded-md bg-gray-100 hover:bg-gray-200"
                                disabled={item.quantity <= 1}
                              >
                                -
                              </button>
                              <input
                                type="number"
                                min="1"
                                value={item.quantity}
                                onChange={(e) => handleUpdateQuantity(index, parseInt(e.target.value) || 1)}
                                className="mx-2 w-16 text-center border border-gray-300 rounded-md p-1"
                              />
                              <button
                                onClick={() => handleUpdateQuantity(index, item.quantity + 1)}
                                className="p-1 rounded-md bg-gray-100 hover:bg-gray-200"
                              >
                                +
                              </button>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-600">
                            ${item.unitPrice.toFixed(2)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-right text-gray-800">
                            ${item.subtotal.toFixed(2)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-center">
                            <button
                              onClick={() => handleRemoveProduct(index)}
                              className="text-red-600 hover:text-red-900"
                            >
                              <Trash2 className="h-5 w-5" />
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                    <tfoot>
                      <tr className="bg-gray-50">
                        <td colSpan={3} className="px-6 py-4 text-right text-sm font-bold text-gray-800">
                          Total:
                        </td>
                        <td className="px-6 py-4 text-right text-lg font-bold text-gray-800">
                          ${total.toFixed(2)}
                        </td>
                        <td></td>
                      </tr>
                    </tfoot>
                  </table>
                </div>
              )}
            </div>

            {/* Save Button */}
            <div className="flex justify-end">
              <button
                onClick={handleSavePurchase}
                disabled={!customer || selectedProducts.length === 0}
                className={`inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm ${
                  !customer || selectedProducts.length === 0
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : 'bg-orange-600 text-white hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500'
                }`}
              >
                <Save className="h-5 w-5 mr-2" />
                Guardar Compra
              </button>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
};

export default NewPurchasePage;
