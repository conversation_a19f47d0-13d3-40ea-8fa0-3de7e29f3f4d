<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DNI Proxy</title>
</head>
<body>
    <script>
        // Función para obtener parámetros de la URL
        function getQueryParam(param) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(param);
        }

        // Función para enviar mensaje al padre
        function sendMessageToParent(data) {
            if (window.parent) {
                window.parent.postMessage(JSON.stringify(data), '*');
            }
        }

        // Función para consultar DNI
        async function consultarDni() {
            const dni = getQueryParam('dni');
            
            if (!dni) {
                sendMessageToParent({
                    success: false,
                    message: 'No se proporcionó un DNI'
                });
                return;
            }

            try {
                const response = await fetch(`https://api-test.altoqueparking.com/api/customer/consultar-dni/${dni}`);
                const data = await response.json();
                
                // Enviar datos al padre
                sendMessageToParent(data);
            } catch (error) {
                sendMessageToParent({
                    success: false,
                    message: 'Error al consultar el DNI: ' + error.message
                });
            }
        }

        // Ejecutar la consulta cuando se carga la página
        window.onload = consultarDni;
    </script>
</body>
</html>
