import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { FaHome, FaSignOutAlt, FaBars, FaTimes } from 'react-icons/fa';

interface EmployeeLayoutProps {
  children: React.ReactNode;
}

const EmployeeLayout: React.FC<EmployeeLayoutProps> = ({ children }) => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('Error al cerrar sesión:', error);
    }
  };

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar para móviles */}
      <div className={`fixed inset-0 z-20 transition-opacity bg-black bg-opacity-50 ${sidebarOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'}`}
        onClick={() => setSidebarOpen(false)}
      />

      {/* Sidebar */}
      <div className={`fixed inset-y-0 left-0 z-30 w-64 bg-green-800 text-white transition-transform transform ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'} md:translate-x-0 md:static md:inset-auto`}>
        <div className="flex items-center justify-between p-4 border-b border-green-700">
          <h1 className="text-xl font-bold">Ferretería</h1>
          <button className="md:hidden" onClick={() => setSidebarOpen(false)}>
            <FaTimes className="h-6 w-6" />
          </button>
        </div>
        
        <div className="p-4 border-b border-green-700">
          <div className="text-sm">Conectado como:</div>
          <div className="font-semibold">{user?.name}</div>
          <div className="text-xs text-green-300">{user?.email}</div>
        </div>
        
        <nav className="p-4">
          <ul className="space-y-2">
            <li>
              <Link to="/employee/dashboard" className="flex items-center p-2 rounded-md hover:bg-green-700">
                <FaHome className="mr-3" />
                Dashboard
              </Link>
            </li>
            <li>
              <button onClick={handleLogout} className="flex items-center w-full text-left p-2 rounded-md hover:bg-green-700">
                <FaSignOutAlt className="mr-3" />
                Cerrar Sesión
              </button>
            </li>
          </ul>
        </nav>
      </div>

      {/* Contenido principal */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Barra superior */}
        <header className="bg-white shadow-sm">
          <div className="flex items-center justify-between p-4">
            <button className="md:hidden" onClick={() => setSidebarOpen(true)}>
              <FaBars className="h-6 w-6" />
            </button>
            <div className="ml-auto">
              <span className="text-sm text-gray-600 mr-2">Empleado</span>
            </div>
          </div>
        </header>

        {/* Contenido */}
        <main className="flex-1 overflow-y-auto">
          {children}
        </main>
      </div>
    </div>
  );
};

export default EmployeeLayout;
