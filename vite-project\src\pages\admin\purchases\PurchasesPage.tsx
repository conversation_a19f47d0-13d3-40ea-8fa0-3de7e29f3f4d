import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import AdminLayout from '../../../components/layouts/AdminLayout';
import {
  ShoppingCart,
  Search,
  Plus,
  Filter,
  ArrowUpDown,
  Edit,
  Trash2,
  Eye,
  ChevronLeft,
  ChevronRight,
  X,
  CheckCircle,
  XCircle,
  Calendar,
  Truck,
  Package
} from 'lucide-react';
import api, { API_ROUTES } from '../../../utils/api';

// Interfaces
interface Supplier {
  id: number;
  name: string;
}

interface Product {
  id: number;
  name: string;
  price: number;
  quantity: number;
}

interface Purchase {
  id: number;
  supplier: string;
  date: string;
  status: 'pending' | 'received' | 'cancelled';
  total: number;
  items: number;
}

const PurchasesPage: React.FC = () => {
  const [purchases, setPurchases] = useState<Purchase[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [showAddModal, setShowAddModal] = useState<boolean>(false);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [itemsPerPage] = useState<number>(10);
  const [sortField, setSortField] = useState<string>('date');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');

  // Proveedores (se cargarán desde el backend)
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);

  // Efecto para cargar los proveedores
  useEffect(() => {
    const fetchSuppliers = async () => {
      try {
        const response = await api.get(API_ROUTES.SUPPLIERS);

        if (response.data.success) {
          setSuppliers(response.data.data);
        } else {
          console.error('Error al cargar proveedores:', response.data.message);
        }
      } catch (err: any) {
        console.error('Error al cargar proveedores:', err);
      }
    };

    fetchSuppliers();
  }, []);

  // Efecto para cargar las compras
  useEffect(() => {
    const fetchPurchases = async () => {
      setLoading(true);
      try {
        // Obtener datos de la API
        const response = await api.get(API_ROUTES.PURCHASES);

        if (response.data.success) {
          // Transformar los datos para que coincidan con nuestra interfaz
          const formattedPurchases = response.data.data.map((purchase: any) => ({
            id: purchase.id,
            supplier: purchase.supplier?.name || 'Proveedor desconocido',
            date: new Date(purchase.date).toLocaleDateString('es-ES'),
            status: purchase.status,
            total: purchase.total,
            items: purchase.items?.length || 0
          }));

          setPurchases(formattedPurchases);
        } else {
          setError('Error al cargar las compras: ' + response.data.message);
        }
      } catch (err: any) {
        console.error('Error al cargar las compras:', err);
        setError('Error al cargar las compras: ' + (err.response?.data?.message || err.message));
      } finally {
        setLoading(false);
      }
    };

    fetchPurchases();
  }, []);

  // Filtrar compras por término de búsqueda y estado
  const filteredPurchases = purchases.filter(purchase => {
    const matchesSearch =
      purchase.supplier.toLowerCase().includes(searchTerm.toLowerCase()) ||
      purchase.id.toString().includes(searchTerm);

    const matchesStatus = selectedStatus === 'all' || purchase.status === selectedStatus;

    return matchesSearch && matchesStatus;
  });

  // Ordenar compras
  const sortedPurchases = [...filteredPurchases].sort((a, b) => {
    if (sortField === 'date') {
      // Convertir fechas (asumiendo formato DD/MM/YYYY)
      const dateA = a.date.split('/').reverse().join('-');
      const dateB = b.date.split('/').reverse().join('-');
      return sortDirection === 'asc'
        ? dateA.localeCompare(dateB)
        : dateB.localeCompare(dateA);
    } else if (sortField === 'total') {
      return sortDirection === 'asc'
        ? a.total - b.total
        : b.total - a.total;
    } else if (sortField === 'supplier') {
      return sortDirection === 'asc'
        ? a.supplier.localeCompare(b.supplier)
        : b.supplier.localeCompare(a.supplier);
    }
    return 0;
  });

  // Paginación
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = sortedPurchases.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(sortedPurchases.length / itemsPerPage);

  // Función para eliminar una compra
  const handleDelete = async (id: number) => {
    if (window.confirm('¿Estás seguro de que deseas eliminar esta compra?')) {
      try {
        const response = await api.delete(`${API_ROUTES.PURCHASES}/${id}`);

        if (response.data.success) {
          setPurchases(purchases.filter(purchase => purchase.id !== id));
          // Mostrar mensaje de éxito (en una implementación real)
        } else {
          setError('Error al eliminar la compra: ' + response.data.message);
        }
      } catch (err: any) {
        console.error('Error al eliminar la compra:', err);
        setError('Error al eliminar la compra: ' + (err.response?.data?.message || err.message));
      }
    }
  };

  // Función para cambiar el campo de ordenamiento
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  return (
    <AdminLayout>
      <div className="p-6 max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-800 mb-1">Gestión de Compras</h1>
            <p className="text-gray-600">Administra las órdenes de compra a proveedores</p>
          </div>
          <div className="mt-4 md:mt-0">
            <Link
              to="/admin/purchases/new"
              className="inline-flex items-center px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 transition-colors"
            >
              <Plus className="h-5 w-5 mr-2" />
              Nueva Compra
            </Link>
          </div>
        </div>

        {/* Error message */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg text-red-700">
            <p className="flex items-center">
              <XCircle className="h-5 w-5 mr-2" />
              {error}
            </p>
          </div>
        )}

        {/* Filters and Search */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div className="relative flex-1">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                placeholder="Buscar por proveedor o número de orden..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="flex items-center space-x-4">
              <div>
                <label htmlFor="status-filter" className="block text-sm font-medium text-gray-700 mb-1">
                  Estado
                </label>
                <select
                  id="status-filter"
                  className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm rounded-md"
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                >
                  <option value="all">Todos</option>
                  <option value="pending">Pendientes</option>
                  <option value="received">Recibidas</option>
                  <option value="cancelled">Canceladas</option>
                </select>
              </div>
              <div>
                <label htmlFor="sort-by" className="block text-sm font-medium text-gray-700 mb-1">
                  Ordenar por
                </label>
                <div className="flex items-center space-x-2">
                  <select
                    id="sort-by"
                    className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm rounded-md"
                    value={sortField}
                    onChange={(e) => handleSort(e.target.value)}
                  >
                    <option value="date">Fecha</option>
                    <option value="supplier">Proveedor</option>
                    <option value="total">Total</option>
                  </select>
                  <button
                    onClick={() => setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')}
                    className="p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500"
                  >
                    <ArrowUpDown className={`h-4 w-4 ${sortDirection === 'asc' ? 'text-orange-600' : 'text-gray-500'}`} />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Purchases Table */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
          {loading ? (
            <div className="p-6">
              <div className="animate-pulse space-y-4">
                {[...Array(5)].map((_, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="h-4 bg-gray-200 rounded w-1/6"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/6"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/6"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/6"></div>
                  </div>
                ))}
              </div>
            </div>
          ) : filteredPurchases.length === 0 ? (
            <div className="p-6 text-center">
              <p className="text-gray-500">No se encontraron órdenes de compra.</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="bg-gray-50">
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Orden #
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Proveedor
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Fecha
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Items
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Total
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Estado
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Acciones
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {currentItems.map((purchase) => (
                    <tr key={purchase.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800">
                        #{purchase.id}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                        {purchase.supplier}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 flex items-center">
                        <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                        {purchase.date}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                        {purchase.items}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                        ${purchase.total.toFixed(2)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        {purchase.status === 'pending' ? (
                          <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-medium">
                            Pendiente
                          </span>
                        ) : purchase.status === 'received' ? (
                          <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">
                            Recibida
                          </span>
                        ) : (
                          <span className="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs font-medium">
                            Cancelada
                          </span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                        <div className="flex space-x-2">
                          <Link
                            to={`/admin/purchases/details/${purchase.id}`}
                            className="text-blue-600 hover:text-blue-900"
                          >
                            <Eye className="h-5 w-5" />
                          </Link>
                          {purchase.status === 'pending' && (
                            <>
                              <Link
                                to={`/admin/purchases/edit/${purchase.id}`}
                                className="text-green-600 hover:text-green-900"
                              >
                                <Edit className="h-5 w-5" />
                              </Link>
                              <button
                                onClick={() => handleDelete(purchase.id)}
                                className="text-red-600 hover:text-red-900"
                              >
                                <Trash2 className="h-5 w-5" />
                              </button>
                            </>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* Pagination */}
          {!loading && filteredPurchases.length > 0 && (
            <div className="px-6 py-4 flex items-center justify-between border-t border-gray-200">
              <div className="flex-1 flex justify-between sm:hidden">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                >
                  Anterior
                </button>
                <button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                >
                  Siguiente
                </button>
              </div>
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    Mostrando <span className="font-medium">{indexOfFirstItem + 1}</span> a{' '}
                    <span className="font-medium">
                      {Math.min(indexOfLastItem, filteredPurchases.length)}
                    </span>{' '}
                    de <span className="font-medium">{filteredPurchases.length}</span> compras
                  </p>
                </div>
                <div>
                  <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    <button
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                      className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                    >
                      <span className="sr-only">Anterior</span>
                      <ChevronLeft className="h-5 w-5" />
                    </button>
                    {[...Array(totalPages)].map((_, index) => (
                      <button
                        key={index}
                        onClick={() => setCurrentPage(index + 1)}
                        className={`relative inline-flex items-center px-4 py-2 border ${
                          currentPage === index + 1
                            ? 'bg-orange-50 border-orange-500 text-orange-600 z-10'
                            : 'border-gray-300 bg-white text-gray-500 hover:bg-gray-50'
                        } text-sm font-medium`}
                      >
                        {index + 1}
                      </button>
                    ))}
                    <button
                      onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                      disabled={currentPage === totalPages}
                      className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                    >
                      <span className="sr-only">Siguiente</span>
                      <ChevronRight className="h-5 w-5" />
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Modal para Nueva Compra */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg p-6 w-full max-w-2xl">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-bold text-gray-900">Nueva Orden de Compra</h3>
              <button
                onClick={() => setShowAddModal(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            <div className="space-y-4">
              <div>
                <label htmlFor="supplier" className="block text-sm font-medium text-gray-700 mb-1">
                  Proveedor
                </label>
                <select
                  id="supplier"
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500"
                >
                  <option value="">Seleccionar proveedor</option>
                  {suppliers.map(supplier => (
                    <option key={supplier.id} value={supplier.id}>
                      {supplier.name}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label htmlFor="date" className="block text-sm font-medium text-gray-700 mb-1">
                  Fecha
                </label>
                <input
                  type="date"
                  id="date"
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500"
                  defaultValue={new Date().toISOString().split('T')[0]}
                />
              </div>
              <div>
                <div className="flex justify-between items-center mb-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Productos
                  </label>
                  <button
                    className="text-sm text-orange-600 hover:text-orange-700 flex items-center"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Agregar Producto
                  </button>
                </div>
                <div className="border border-gray-300 rounded-md overflow-hidden">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Producto
                        </th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Cantidad
                        </th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Precio
                        </th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Subtotal
                        </th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Acciones
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      <tr>
                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-600">
                          <select className="w-full p-1 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500">
                            <option value="">Seleccionar producto</option>
                            <option>Martillo de Carpintero</option>
                            <option>Destornillador Phillips</option>
                            <option>Pintura Látex 20L</option>
                          </select>
                        </td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-600">
                          <input
                            type="number"
                            min="1"
                            className="w-full p-1 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500"
                            defaultValue="1"
                          />
                        </td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-600">
                          <input
                            type="number"
                            min="0"
                            step="0.01"
                            className="w-full p-1 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500"
                            defaultValue="0.00"
                          />
                        </td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-600">
                          $0.00
                        </td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-600">
                          <button className="text-red-600 hover:text-red-900">
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
              <div className="flex justify-between items-center pt-4 border-t border-gray-200">
                <div className="text-sm text-gray-700">
                  <p><strong>Total Items:</strong> 1</p>
                </div>
                <div className="text-lg font-bold text-gray-900">
                  <p>Total: $0.00</p>
                </div>
              </div>
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => setShowAddModal(false)}
                  className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-300"
                >
                  Cancelar
                </button>
                <button
                  className="px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500"
                >
                  Guardar Compra
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </AdminLayout>
  );
};

export default PurchasesPage;
