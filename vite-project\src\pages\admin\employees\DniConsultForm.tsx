import React, { useState, useRef, useEffect } from 'react';
import { CreditCard, Search, Loader } from 'lucide-react';

interface DniConsultFormProps {
  onDniDataReceived: (data: {
    dni: string;
    nombres: string;
    apellidos: string;
    fullName: string;
  }) => void;
  initialDni?: string;
}

const DniConsultForm: React.FC<DniConsultFormProps> = ({ onDniDataReceived, initialDni = '' }) => {
  const [dni, setDni] = useState<string>(initialDni);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const formRef = useRef<HTMLFormElement>(null);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // Función para manejar el cambio en el campo de DNI
  const handleDniChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, '').slice(0, 8);
    setDni(value);
  };

  // Función para consultar el DNI
  const handleConsultDni = (e: React.FormEvent) => {
    e.preventDefault();

    if (!dni || dni.length !== 8) {
      setError('El DNI debe tener 8 dígitos');
      return;
    }

    // Primero, verificar si ya tenemos este DNI en localStorage
    const existingData = JSON.parse(localStorage.getItem('dniConsults') || '[]');
    const dniData = existingData.find((item: any) => item.dni === dni);

    if (dniData) {
      // Si ya tenemos los datos en localStorage, usarlos
      onDniDataReceived({
        dni: dniData.dni,
        nombres: dniData.nombres,
        apellidos: dniData.apellidos,
        fullName: dniData.fullName
      });
      return;
    }

    setIsLoading(true);
    setError(null);

    // Intentar consultar el DNI usando un método alternativo
    // Crear un iframe oculto
    if (formRef.current) {
      formRef.current.submit();
    }
  };

  // Efecto para escuchar mensajes del iframe
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      // Verificar que el mensaje viene de nuestro iframe
      if (event.source === iframeRef.current?.contentWindow) {
        try {
          const data = JSON.parse(event.data);
          if (data.success) {
            // Procesar los datos recibidos
            const { dni: receivedDni, nombres, apellidos } = data;
            const fullName = `${nombres} ${apellidos}`.trim();

            // Llamar al callback con los datos
            onDniDataReceived({
              dni: receivedDni,
              nombres,
              apellidos,
              fullName
            });

            // Guardar en localStorage
            const existingData = JSON.parse(localStorage.getItem('dniConsults') || '[]');
            const dniData = {
              dni: receivedDni,
              nombres,
              apellidos,
              fullName,
              timestamp: new Date().toISOString()
            };

            // Verificar si este DNI ya existe
            const existingIndex = existingData.findIndex((item: any) => item.dni === receivedDni);

            if (existingIndex >= 0) {
              // Actualizar datos existentes
              existingData[existingIndex] = dniData;
            } else {
              // Agregar nuevos datos
              existingData.push(dniData);
            }

            // Guardar en localStorage
            localStorage.setItem('dniConsults', JSON.stringify(existingData));
          } else {
            setError('No se pudo obtener información del DNI');
          }
        } catch (err) {
          setError('Error al procesar la respuesta');
        } finally {
          setIsLoading(false);
        }
      }
    };

    // Agregar el event listener
    window.addEventListener('message', handleMessage);

    // Limpiar el event listener cuando el componente se desmonte
    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [onDniDataReceived]);

  // Función para manejar la entrada manual
  const handleManualEntry = () => {
    const nombres = prompt('Ingresa los nombres:') || '';
    const apellidos = prompt('Ingresa los apellidos:') || '';

    if (nombres && apellidos) {
      const fullName = `${nombres} ${apellidos}`.trim();

      // Llamar al callback con los datos
      onDniDataReceived({
        dni,
        nombres,
        apellidos,
        fullName
      });

      // Guardar en localStorage
      const existingData = JSON.parse(localStorage.getItem('dniConsults') || '[]');
      const dniData = {
        dni,
        nombres,
        apellidos,
        fullName,
        timestamp: new Date().toISOString(),
        manual: true
      };

      existingData.push(dniData);
      localStorage.setItem('dniConsults', JSON.stringify(existingData));
    }
  };

  return (
    <div className="space-y-4">
      <form ref={formRef} target="dniConsultFrame" method="GET" action="/dni-proxy.html" onSubmit={handleConsultDni}>
        <input type="hidden" name="dni" value={dni} />
        <div className="flex items-end space-x-2">
          <div className="flex-1">
            <label htmlFor="dni-input" className="block text-sm font-medium text-gray-700 mb-1">
              DNI
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <CreditCard className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                id="dni-input"
                value={dni}
                onChange={handleDniChange}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                placeholder="Ingrese el DNI"
                maxLength={8}
              />
            </div>
          </div>
          <div className="flex space-x-2">
            <button
              type="submit"
              disabled={isLoading || !dni || dni.length !== 8}
              className={`inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md ${
                isLoading || !dni || dni.length !== 8
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : 'bg-orange-600 text-white hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500'
              }`}
            >
              {isLoading ? (
                <Loader className="h-4 w-4 animate-spin" />
              ) : (
                <Search className="h-4 w-4" />
              )}
              <span className="ml-1">Buscar</span>
            </button>
            <button
              type="button"
              onClick={handleManualEntry}
              className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
            >
              Manual
            </button>
          </div>
        </div>
        {error && (
          <p className="mt-2 text-sm text-red-600">{error}</p>
        )}
      </form>

      {/* Iframe oculto para recibir la respuesta */}
      <iframe
        ref={iframeRef}
        name="dniConsultFrame"
        style={{ display: 'none' }}
        title="DNI Consult"
        src="about:blank"
      />
    </div>
  );
};

export default DniConsultForm;
