<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\DniConsult;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class DniConsultController extends Controller
{
    /**
     * Consulta un DNI en la API externa, guarda los datos en la BD y devuelve los datos
     *
     * @param  string  $dni
     * @return \Illuminate\Http\JsonResponse
     */
    public function consultarDni($dni)
    {
        try {
            // Validar que el DNI tenga 8 dígitos
            if (!preg_match('/^\d{8}$/', $dni)) {
                return response()->json([
                    'success' => false,
                    'message' => 'El DNI debe tener 8 dígitos numéricos'
                ], 400);
            }

            // Verificar si ya tenemos este DNI en nuestra base de datos
            $dniConsult = DniConsult::where('dni', $dni)->first();

            if ($dniConsult) {
                // Si ya existe, devolvemos los datos almacenados
                return response()->json([
                    'success' => true,
                    'data' => [
                        'nombres' => $dniConsult->nombres,
                        'apellidoPaterno' => $dniConsult->apellido_paterno,
                        'apellidoMaterno' => $dniConsult->apellido_materno,
                        'numeroDocumento' => $dniConsult->dni
                    ],
                    'source' => 'database'
                ]);
            }

            // Si no existe en la BD, hacer la solicitud a la API externa
            $response = Http::get("https://api-test.altoqueparking.com/api/customer/consultar-dni/{$dni}");

            // Verificar si la solicitud fue exitosa
            if ($response->successful()) {
                // Obtener los datos de la respuesta
                $data = $response->json();

                // Extraer los apellidos (asumiendo que vienen en formato "APELLIDO1 APELLIDO2")
                $apellidos = $data['apellidos'] ?? '';
                $apellidosArray = explode(' ', $apellidos);
                $apellidoPaterno = $apellidosArray[0] ?? '';
                $apellidoMaterno = $apellidosArray[1] ?? '';

                // Crear un nuevo registro en la base de datos
                $dniConsult = DniConsult::create([
                    'dni' => $dni,
                    'nombres' => $data['nombres'] ?? '',
                    'apellido_paterno' => $apellidoPaterno,
                    'apellido_materno' => $apellidoMaterno,
                    'nombre_completo' => $data['nombre'] ?? '',
                    'response_json' => $data
                ]);

                // Devolver la respuesta formateada
                return response()->json([
                    'success' => true,
                    'data' => [
                        'nombres' => $dniConsult->nombres,
                        'apellidoPaterno' => $dniConsult->apellido_paterno,
                        'apellidoMaterno' => $dniConsult->apellido_materno,
                        'numeroDocumento' => $dniConsult->dni
                    ],
                    'source' => 'api'
                ]);
            } else {
                // Si la API externa devuelve un error
                return response()->json([
                    'success' => false,
                    'message' => 'No se pudo obtener información del DNI'
                ], $response->status());
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error al consultar el DNI: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Obtiene todos los DNIs consultados
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        try {
            $dniConsults = DniConsult::orderBy('created_at', 'desc')->get();

            return response()->json([
                'success' => true,
                'data' => $dniConsults
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error al obtener las consultas de DNI: ' . $e->getMessage()
            ], 500);
        }
    }
}
