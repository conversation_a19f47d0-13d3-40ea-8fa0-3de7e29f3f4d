<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;

class EmployeeController extends Controller
{
    /**
     * Mostrar una lista de empleados.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $employees = User::where('role', 'employee')->get();
        return response()->json($employees);
    }

    /**
     * Almacenar un nuevo empleado.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'phone' => 'nullable|string|max:20',
            'dni' => 'nullable|string|max:20',
            'password' => 'required|string|min:6',
        ]);

        $validated['role'] = 'employee';
        $validated['password'] = Hash::make($validated['password']);

        $employee = User::create($validated);

        return response()->json([
            'message' => 'Empleado creado exitosamente',
            'employee' => $employee
        ], 201);
    }

    /**
     * Mostrar un empleado específico.
     *
     * @param  \App\Models\User  $employee
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(User $employee)
    {
        // Verificar que el usuario es un empleado
        if (!$employee->isEmployee()) {
            return response()->json(['message' => 'Empleado no encontrado'], 404);
        }

        return response()->json($employee);
    }

    /**
     * Actualizar un empleado específico.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\User  $employee
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, User $employee)
    {
        // Verificar que el usuario es un empleado
        if (!$employee->isEmployee()) {
            return response()->json(['message' => 'Empleado no encontrado'], 404);
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => [
                'required',
                'string',
                'email',
                'max:255',
                Rule::unique('users')->ignore($employee->id),
            ],
            'phone' => 'nullable|string|max:20',
            'dni' => 'nullable|string|max:20',
            'password' => 'nullable|string|min:6',
        ]);

        // Solo actualizar la contraseña si se proporciona
        if (isset($validated['password']) && $validated['password']) {
            $validated['password'] = Hash::make($validated['password']);
        } else {
            unset($validated['password']);
        }

        $employee->update($validated);

        return response()->json([
            'message' => 'Empleado actualizado exitosamente',
            'employee' => $employee
        ]);
    }

    /**
     * Eliminar un empleado específico.
     *
     * @param  \App\Models\User  $employee
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(User $employee)
    {
        // Verificar que el usuario es un empleado
        if (!$employee->isEmployee()) {
            return response()->json(['message' => 'Empleado no encontrado'], 404);
        }

        $employee->delete();

        return response()->json([
            'message' => 'Empleado eliminado exitosamente'
        ]);
    }
}
