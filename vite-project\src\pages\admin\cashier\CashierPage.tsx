import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import AdminLayout from '../../../components/layouts/AdminLayout';
import {
  CircleDollarSign,
  Search,
  Plus,
  Clock,
  Calendar,
  User,
  ArrowUpDown,
  ChevronLeft,
  ChevronRight,
  X,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { useAuth } from '../../../contexts/AuthContext';
import api, { API_ROUTES } from '../../../utils/api';

// Interfaces
interface CashRegister {
  id: number;
  user_name: string;
  open_date: string;
  open_time: string;
  initial_amount: number;
  status: 'open' | 'closed';
  details?: string;
  close_date?: string;
  close_time?: string;
  close_details?: string;
  final_amount?: number;
}

const CashierPage: React.FC = () => {
  const { user } = useAuth();
  const [cashRegisters, setCashRegisters] = useState<CashRegister[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [showOpenModal, setShowOpenModal] = useState<boolean>(false);
  const [showCloseModal, setShowCloseModal] = useState<boolean>(false);
  const [initialAmount, setInitialAmount] = useState<string>('0');
  const [finalAmount, setFinalAmount] = useState<string>('0');
  const [openDetails, setOpenDetails] = useState<string>('');
  const [closeDetails, setCloseDetails] = useState<string>('');
  const [selectedRegisterId, setSelectedRegisterId] = useState<number | null>(null);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [itemsPerPage] = useState<number>(10);

  // Efecto para cargar los registros de caja
  useEffect(() => {
    const fetchCashRegisters = async () => {
      setLoading(true);
      try {
        // Obtener datos de la API
        const response = await api.get(API_ROUTES.CASH_REGISTERS);

        if (response.data.success) {
          setCashRegisters(response.data.data);
        } else {
          setError('Error al cargar los registros de caja: ' + response.data.message);
        }
      } catch (err: any) {
        console.error('Error al cargar los registros de caja:', err);
        setError('Error al cargar los registros de caja: ' + (err.response?.data?.message || err.message));
      } finally {
        setLoading(false);
      }
    };

    fetchCashRegisters();
  }, []);

  // Función para abrir caja
  const handleOpenCashRegister = async () => {
    try {
      // Crear datos para enviar a la API
      const cashRegisterData = {
        initial_amount: parseFloat(initialAmount) || 0,
        details: openDetails.trim() || undefined
      };

      // Enviar datos a la API
      const response = await api.post(API_ROUTES.OPEN_CASH_REGISTER, cashRegisterData);

      if (response.data.success) {
        // Actualizar el estado con los datos devueltos por la API
        setCashRegisters([...cashRegisters, response.data.data]);
        setSuccess('Caja abierta exitosamente');
        setShowOpenModal(false);
        setInitialAmount('0');
        setOpenDetails('');
      } else {
        setError('Error al abrir caja: ' + response.data.message);
      }
    } catch (err: any) {
      console.error('Error al abrir caja:', err);
      setError('Error al abrir caja: ' + (err.response?.data?.message || err.message));
    }
  };

  // Función para cerrar caja
  const handleCloseCashRegister = async () => {
    if (!selectedRegisterId) return;

    try {
      // Crear datos para enviar a la API
      const closeData = {
        final_amount: parseFloat(finalAmount) || 0,
        close_details: closeDetails.trim() || undefined
      };

      // Enviar datos a la API
      const response = await api.put(API_ROUTES.CLOSE_CASH_REGISTER(selectedRegisterId), closeData);

      if (response.data.success) {
        // Actualizar el estado con los datos devueltos por la API
        setCashRegisters(cashRegisters.map(register =>
          register.id === selectedRegisterId ? response.data.data : register
        ));
        setSuccess('Caja cerrada exitosamente');
        setShowCloseModal(false);
        setFinalAmount('0');
        setCloseDetails('');
        setSelectedRegisterId(null);
      } else {
        setError('Error al cerrar caja: ' + response.data.message);
      }
    } catch (err: any) {
      console.error('Error al cerrar caja:', err);
      setError('Error al cerrar caja: ' + (err.response?.data?.message || err.message));
    }
  };

  // Paginación
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = cashRegisters.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(cashRegisters.length / itemsPerPage);

  return (
    <AdminLayout>
      <div className="p-6 max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-800 mb-1">Apertura de Caja</h1>
            <p className="text-gray-600">Gestiona la apertura y cierre de caja diaria</p>
          </div>
          <div className="mt-4 md:mt-0">
            <button
              onClick={() => setShowOpenModal(true)}
              className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors"
              disabled={cashRegisters.some(register => register.status === 'open')}
            >
              <Plus className="h-5 w-5 mr-2" />
              Abrir Caja
            </button>
          </div>
        </div>

        {/* Error message */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg text-red-700">
            <p className="flex items-center">
              <XCircle className="h-5 w-5 mr-2" />
              {error}
            </p>
          </div>
        )}

        {/* Cash Register Table */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
          <div className="p-6 border-b border-gray-100">
            <h2 className="text-lg font-bold text-gray-800">Historial de Apertura de Caja</h2>
          </div>

          {loading ? (
            <div className="p-6">
              <div className="animate-pulse space-y-4">
                {[...Array(5)].map((_, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="h-4 bg-gray-200 rounded w-1/6"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/6"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/6"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/6"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/6"></div>
                  </div>
                ))}
              </div>
            </div>
          ) : cashRegisters.length === 0 ? (
            <div className="p-6 text-center">
              <p className="text-gray-500">No hay registros de apertura de caja.</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="bg-gray-50">
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      ID
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Usuario
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Fecha Apertura
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Hora Apertura
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Monto Inicial
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Detalles
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Estado
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Acciones
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {currentItems.map((register) => (
                    <tr key={register.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800">
                        #{register.id}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                        {register.user_name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                        {register.open_date}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                        {register.open_time}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                        ${register.initial_amount.toFixed(2)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                        {register.details || '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        {register.status === 'open' ? (
                          <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">
                            Abierta
                          </span>
                        ) : (
                          <span className="px-2 py-1 bg-gray-100 text-gray-800 rounded-full text-xs font-medium">
                            Cerrada
                          </span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                        {register.status === 'open' && (
                          <button
                            onClick={() => {
                              setSelectedRegisterId(register.id);
                              setShowCloseModal(true);
                            }}
                            className="text-red-600 hover:text-red-900 font-medium"
                          >
                            Cerrar Caja
                          </button>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* Pagination */}
          {!loading && cashRegisters.length > 0 && (
            <div className="px-6 py-4 flex items-center justify-between border-t border-gray-200">
              <div className="flex-1 flex justify-between sm:hidden">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                >
                  Anterior
                </button>
                <button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                >
                  Siguiente
                </button>
              </div>
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    Mostrando <span className="font-medium">{indexOfFirstItem + 1}</span> a{' '}
                    <span className="font-medium">
                      {Math.min(indexOfLastItem, cashRegisters.length)}
                    </span>{' '}
                    de <span className="font-medium">{cashRegisters.length}</span> registros
                  </p>
                </div>
                <div>
                  <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    <button
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                      className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                    >
                      <span className="sr-only">Anterior</span>
                      <ChevronLeft className="h-5 w-5" />
                    </button>
                    {[...Array(totalPages)].map((_, index) => (
                      <button
                        key={index}
                        onClick={() => setCurrentPage(index + 1)}
                        className={`relative inline-flex items-center px-4 py-2 border ${
                          currentPage === index + 1
                            ? 'bg-orange-50 border-orange-500 text-orange-600 z-10'
                            : 'border-gray-300 bg-white text-gray-500 hover:bg-gray-50'
                        } text-sm font-medium`}
                      >
                        {index + 1}
                      </button>
                    ))}
                    <button
                      onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                      disabled={currentPage === totalPages}
                      className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                    >
                      <span className="sr-only">Siguiente</span>
                      <ChevronRight className="h-5 w-5" />
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Modal para Abrir Caja */}
      {showOpenModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-bold text-gray-900">Apertura de Caja</h3>
              <button
                onClick={() => setShowOpenModal(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Usuario
                </label>
                <div className="flex items-center bg-gray-100 p-2 rounded-md">
                  <User className="h-5 w-5 text-gray-500 mr-2" />
                  <span>{user?.name || 'Usuario'}</span>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Fecha y Hora
                </label>
                <div className="flex items-center bg-gray-100 p-2 rounded-md">
                  <Calendar className="h-5 w-5 text-gray-500 mr-2" />
                  <span>{new Date().toLocaleDateString('es-ES')}</span>
                  <Clock className="h-5 w-5 text-gray-500 ml-4 mr-2" />
                  <span>
                    {new Date().toLocaleTimeString('es-ES', {
                      hour: '2-digit',
                      minute: '2-digit',
                      hour12: true
                    })}
                  </span>
                </div>
              </div>
              <div>
                <label htmlFor="initialAmount" className="block text-sm font-medium text-gray-700 mb-1">
                  Monto Inicial ($)
                </label>
                <input
                  type="number"
                  id="initialAmount"
                  value={initialAmount}
                  onChange={(e) => setInitialAmount(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500"
                  min="0"
                  step="0.01"
                />
              </div>
              <div>
                <label htmlFor="openDetails" className="block text-sm font-medium text-gray-700 mb-1">
                  Detalles (opcional)
                </label>
                <textarea
                  id="openDetails"
                  value={openDetails}
                  onChange={(e) => setOpenDetails(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500"
                  rows={3}
                  placeholder="Ingrese detalles adicionales si es necesario"
                />
              </div>
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => setShowOpenModal(false)}
                  className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-300"
                >
                  Cancelar
                </button>
                <button
                  onClick={handleOpenCashRegister}
                  className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
                >
                  Abrir Caja
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Modal para Cerrar Caja */}
      {showCloseModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-bold text-gray-900">Cierre de Caja</h3>
              <button
                onClick={() => {
                  setShowCloseModal(false);
                  setSelectedRegisterId(null);
                }}
                className="text-gray-400 hover:text-gray-500"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Usuario
                </label>
                <div className="flex items-center bg-gray-100 p-2 rounded-md">
                  <User className="h-5 w-5 text-gray-500 mr-2" />
                  <span>{user?.name || 'Usuario'}</span>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Fecha y Hora
                </label>
                <div className="flex items-center bg-gray-100 p-2 rounded-md">
                  <Calendar className="h-5 w-5 text-gray-500 mr-2" />
                  <span>{new Date().toLocaleDateString('es-ES')}</span>
                  <Clock className="h-5 w-5 text-gray-500 ml-4 mr-2" />
                  <span>
                    {new Date().toLocaleTimeString('es-ES', {
                      hour: '2-digit',
                      minute: '2-digit',
                      hour12: true
                    })}
                  </span>
                </div>
              </div>
              <div>
                <label htmlFor="finalAmount" className="block text-sm font-medium text-gray-700 mb-1">
                  Monto Final ($)
                </label>
                <input
                  type="number"
                  id="finalAmount"
                  value={finalAmount}
                  onChange={(e) => setFinalAmount(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500"
                  min="0"
                  step="0.01"
                />
              </div>
              <div>
                <label htmlFor="closeDetails" className="block text-sm font-medium text-gray-700 mb-1">
                  Detalles (opcional)
                </label>
                <textarea
                  id="closeDetails"
                  value={closeDetails}
                  onChange={(e) => setCloseDetails(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500"
                  rows={3}
                  placeholder="Ingrese detalles adicionales si es necesario"
                />
              </div>
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => {
                    setShowCloseModal(false);
                    setSelectedRegisterId(null);
                  }}
                  className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-300"
                >
                  Cancelar
                </button>
                <button
                  onClick={handleCloseCashRegister}
                  className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
                >
                  Cerrar Caja
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </AdminLayout>
  );
};

export default CashierPage;
