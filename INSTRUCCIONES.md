# Instrucciones para ejecutar el proyecto de Ferretería

## Instalación de dependencias del frontend

Abre una terminal (CMD o PowerShell) y ejecuta los siguientes comandos:

```bash
cd vite-project
npm install react-router-dom axios react-icons
npm install -D tailwindcss postcss autoprefixer
```

## Iniciar el servidor de desarrollo del frontend

```bash
cd vite-project
npm run dev
```

## Iniciar el servidor de backend (Laravel)

Abre otra terminal y ejecuta:

```bash
cd ferreteria_backend
php artisan serve
```

## Credenciales de acceso

- **Administrador**:
  - Email: <EMAIL>
  - Contraseña: password

## Solución de problemas comunes

### Error: Failed to resolve import "react-router-dom"

Este error ocurre porque el paquete `react-router-dom` no está instalado. Ejecuta:

```bash
cd vite-project
npm install react-router-dom
```

### Error: Failed to resolve import "axios"

```bash
cd vite-project
npm install axios
```

### Error: Failed to resolve import "react-icons"

```bash
cd vite-project
npm install react-icons
```

### Error: PostCSS plugin tailwindcss requires PostCSS 8

```bash
cd vite-project
npm install -D tailwindcss@latest postcss@latest autoprefixer@latest
```

## Verificación de la instalación

Para verificar que todas las dependencias están correctamente instaladas, ejecuta:

```bash
cd vite-project
npm list react-router-dom axios react-icons tailwindcss postcss autoprefixer
```
