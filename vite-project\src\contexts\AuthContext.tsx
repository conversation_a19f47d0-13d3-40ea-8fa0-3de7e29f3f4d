import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import axios from 'axios';

// Definir interfaces
export interface LoginCredentials {
  email: string;
  password: string;
}

export interface User {
  id: number;
  name: string;
  email: string;
  phone?: string;
  dni?: string;
  role: 'admin' | 'employee';
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  isAdmin: boolean;
  isEmployee: boolean;
  login: (credentials: LoginCredentials) => Promise<any>; // Cambiado a Promise<any> para devolver la respuesta
  logout: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth debe ser usado dentro de un AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Función para obtener el usuario actual del localStorage
  const getCurrentUser = (): User | null => {
    const userStr = localStorage.getItem('user');
    if (userStr) {
      return JSON.parse(userStr);
    }
    return null;
  };

  useEffect(() => {
    // Verificar si hay un usuario en localStorage al cargar la aplicación
    const loadUser = () => {
      const currentUser = getCurrentUser();
      setUser(currentUser);
      setIsLoading(false);
    };

    loadUser();
  }, []);

  const login = async (credentials: LoginCredentials) => {
    setIsLoading(true);
    try {
      // Configuración para evitar problemas de CORS
      const config = {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        }
      };

      // Datos simplificados
      const data = {
        email: credentials.email,
        password: credentials.password
      };

      console.log('Intentando login con:', data);

      // Usar el proxy de Vite para evitar problemas de CORS
      const response = await axios.post(
        '/api/login', // Esto usará el proxy configurado en vite.config.ts
        data,
        config
      );

      console.log('Respuesta del servidor:', response.data);

      // Verificar la respuesta
      if (!response.data || !response.data.token || !response.data.user) {
        console.error('Respuesta inválida:', response.data);
        throw new Error('Respuesta de autenticación inválida');
      }

      // Guardar token y usuario en localStorage
      localStorage.setItem('token', response.data.token);
      localStorage.setItem('user', JSON.stringify(response.data.user));

      // Actualizar el estado
      setUser(response.data.user);

      return response.data;
    } catch (error: any) {
      console.error('Error de login:', error);

      // Mostrar detalles del error para depuración
      if (error.response) {
        console.error('Respuesta de error:', error.response.data);
        console.error('Estado HTTP:', error.response.status);
      } else if (error.request) {
        console.error('No se recibió respuesta:', error.request);
      } else {
        console.error('Error al configurar la solicitud:', error.message);
      }

      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    setIsLoading(true);
    try {
      // Usar el proxy de Vite para evitar problemas de CORS
      await axios.post('/api/logout');

      // Limpiar localStorage
      localStorage.removeItem('token');
      localStorage.removeItem('user');

      // Actualizar el estado
      setUser(null);
    } catch (error) {
      console.error('Error al cerrar sesión:', error);

      // Limpiar localStorage de todas formas
      localStorage.removeItem('token');
      localStorage.removeItem('user');

      // Actualizar el estado
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  const value = {
    user,
    isLoading,
    isAuthenticated: !!user,
    isAdmin: user?.role === 'admin' || false,
    isEmployee: user?.role === 'employee' || false,
    login,
    logout,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
