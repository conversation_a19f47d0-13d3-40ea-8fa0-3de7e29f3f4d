import React, { useState } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { Users, Home, LogOut, Menu, X, Package, ShoppingCart, BarChart2, Settings, CircleDollarSign, ChevronDown, Bell, Search, HelpCircle, Tag } from 'lucide-react';
import ThemeSelector from '../ThemeSelector';
import { useTheme } from '../../contexts/ThemeContext';

interface AdminLayoutProps {
  children: React.ReactNode;
}

const AdminLayout: React.FC<AdminLayoutProps> = ({ children }) => {
  const { user, logout } = useAuth();
  const { theme } = useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [userMenuOpen, setUserMenuOpen] = useState(false);

  // Función para verificar si un enlace está activo
  const isActive = (path: string) => {
    return location.pathname.startsWith(path);
  };

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('Error al cerrar sesión:', error);
    }
  };

  // Obtener el título de la página actual
  const getPageTitle = () => {
    if (location.pathname.includes('/admin/dashboard')) return 'Dashboard';
    if (location.pathname.includes('/admin/employees')) return 'Gestión de Empleados';
    if (location.pathname.includes('/admin/inventory')) return 'Inventario';
    if (location.pathname.includes('/admin/cashier')) return 'Apertura de Caja';
    if (location.pathname.includes('/admin/purchases')) return 'Compras';
    if (location.pathname.includes('/admin/reports')) return 'Reportes';
    if (location.pathname.includes('/admin/settings')) return 'Configuración';
    return 'Ferretería Pro';
  };

  return (
    <div className={`flex h-screen ${theme === 'dark' ? 'bg-dark-bg text-dark-text-primary' : 'bg-gray-50 text-gray-800'}`}>
      {/* Overlay para móviles */}
      <div
        className={`fixed inset-0 z-20 transition-opacity bg-black bg-opacity-50 ${
          sidebarOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
        }`}
        onClick={() => setSidebarOpen(false)}
      />

      {/* Sidebar */}
      <div
        className={`fixed inset-y-0 left-0 z-30 w-64 ${
          theme === 'dark' ? 'bg-dark-card border-r border-dark-border' : 'bg-white'
        } shadow-lg transition-transform transform ${
          sidebarOpen ? 'translate-x-0' : '-translate-x-full'
        } md:translate-x-0 md:static md:inset-auto`}
      >
        <div className="flex flex-col h-full">
          {/* Logo */}
          <div className={`flex items-center justify-between p-4 border-b ${theme === 'dark' ? 'border-dark-border' : 'border-gray-200'}`}>
            <div className="flex items-center space-x-2">
              <div className="bg-orange-600 text-white p-1.5 rounded">
                <Package className="h-5 w-5" />
              </div>
              <h1 className={`text-xl font-bold ${theme === 'dark' ? 'text-white' : 'text-gray-800'}`}>Ferretería Pro</h1>
            </div>
            <div className="flex items-center space-x-2">
              <ThemeSelector />
              <button className="md:hidden text-gray-500 hover:text-gray-700" onClick={() => setSidebarOpen(false)}>
                <X className="h-5 w-5" />
              </button>
            </div>
          </div>

          {/* User info */}
          <div className={`p-4 border-b ${theme === 'dark' ? 'bg-dark-card border-dark-border' : 'bg-gray-50 border-gray-200'}`}>
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 rounded-full bg-gradient-to-r from-orange-500 to-amber-600 flex items-center justify-center text-white font-bold">
                {user?.name?.charAt(0).toUpperCase()}
              </div>
              <div className="flex-1 min-w-0">
                <p className={`text-sm font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-900'} truncate`}>{user?.name}</p>
                <p className={`text-xs ${theme === 'dark' ? 'text-dark-text-secondary' : 'text-gray-500'} truncate`}>{user?.email}</p>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <div className="flex-1 overflow-y-auto py-2 px-3">
            <div className="space-y-1 mb-4">
              <Link
                to="/admin/dashboard"
                className={`flex items-center px-3 py-2.5 text-sm font-medium rounded-lg ${
                  isActive('/admin/dashboard')
                    ? 'bg-orange-50 text-orange-600'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                <Home className={`mr-3 h-5 w-5 ${isActive('/admin/dashboard') ? 'text-orange-600' : 'text-gray-500'}`} />
                Dashboard
              </Link>
            </div>

            <div className="pt-2 pb-1">
              <p className="px-3 text-xs font-semibold text-gray-400 uppercase tracking-wider">
                Gestión
              </p>
            </div>

            <div className="space-y-1 mb-4">
              <Link
                to="/admin/employees"
                className={`flex items-center px-3 py-2.5 text-sm font-medium rounded-lg ${
                  isActive('/admin/employees')
                    ? 'bg-orange-50 text-orange-600'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                <Users className={`mr-3 h-5 w-5 ${isActive('/admin/employees') ? 'text-orange-600' : 'text-gray-500'}`} />
                Empleados
              </Link>
              <Link
                to="/admin/inventory"
                className={`flex items-center px-3 py-2.5 text-sm font-medium rounded-lg ${
                  isActive('/admin/inventory') && !isActive('/admin/inventory/categories')
                    ? 'bg-orange-50 text-orange-600'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                <Package className={`mr-3 h-5 w-5 ${isActive('/admin/inventory') && !isActive('/admin/inventory/categories') ? 'text-orange-600' : 'text-gray-500'}`} />
                Inventario
              </Link>
              <Link
                to="/admin/inventory/categories"
                className={`flex items-center px-3 py-2.5 text-sm font-medium rounded-lg ml-6 ${
                  isActive('/admin/inventory/categories')
                    ? 'bg-orange-50 text-orange-600'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                <Tag className={`mr-3 h-5 w-5 ${isActive('/admin/inventory/categories') ? 'text-orange-600' : 'text-gray-500'}`} />
                Categorías
              </Link>
              <Link
                to="/admin/cashier"
                className={`flex items-center px-3 py-2.5 text-sm font-medium rounded-lg ${
                  isActive('/admin/cashier')
                    ? 'bg-orange-50 text-orange-600'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                <CircleDollarSign className={`mr-3 h-5 w-5 ${isActive('/admin/cashier') ? 'text-orange-600' : 'text-gray-500'}`} />
                Apertura de Caja
              </Link>
              <Link
                to="/admin/purchases"
                className={`flex items-center px-3 py-2.5 text-sm font-medium rounded-lg ${
                  isActive('/admin/purchases')
                    ? 'bg-orange-50 text-orange-600'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                <ShoppingCart className={`mr-3 h-5 w-5 ${isActive('/admin/purchases') ? 'text-orange-600' : 'text-gray-500'}`} />
                Compras
              </Link>
            </div>

            <div className="pt-2 pb-1">
              <p className="px-3 text-xs font-semibold text-gray-400 uppercase tracking-wider">
                Análisis
              </p>
            </div>

            <div className="space-y-1 mb-4">
              <Link
                to="/admin/reports"
                className={`flex items-center px-3 py-2.5 text-sm font-medium rounded-lg ${
                  isActive('/admin/reports')
                    ? 'bg-orange-50 text-orange-600'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                <BarChart2 className={`mr-3 h-5 w-5 ${isActive('/admin/reports') ? 'text-orange-600' : 'text-gray-500'}`} />
                Reportes
              </Link>
            </div>

            <div className="pt-2 pb-1">
              <p className="px-3 text-xs font-semibold text-gray-400 uppercase tracking-wider">
                Sistema
              </p>
            </div>

            <div className="space-y-1">
              <Link
                to="/admin/settings"
                className={`flex items-center px-3 py-2.5 text-sm font-medium rounded-lg ${
                  isActive('/admin/settings')
                    ? 'bg-orange-50 text-orange-600'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                <Settings className={`mr-3 h-5 w-5 ${isActive('/admin/settings') ? 'text-orange-600' : 'text-gray-500'}`} />
                Configuración
              </Link>
              <button
                onClick={handleLogout}
                className="w-full flex items-center px-3 py-2.5 text-sm font-medium text-gray-700 rounded-lg hover:bg-gray-100"
              >
                <LogOut className="mr-3 h-5 w-5 text-gray-500" />
                Cerrar Sesión
              </button>
            </div>
          </div>

          {/* Pro version banner */}
          <div className="p-4 mt-auto">
            <div className="bg-gradient-to-r from-orange-500 to-amber-600 rounded-lg p-3 text-white">
              <p className="text-sm font-medium">Ferretería Pro v1.0</p>
              <p className="text-xs mt-1 opacity-90">Sistema de gestión profesional</p>
            </div>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top navbar */}
        <header className={`${theme === 'dark' ? 'bg-dark-card border-b border-dark-border' : 'bg-white border-b border-gray-200'} shadow-sm`}>
          <div className="flex items-center justify-between px-4 py-3">
            <div className="flex items-center">
              <button
                className={`md:hidden mr-3 ${theme === 'dark' ? 'text-gray-400 hover:text-gray-300' : 'text-gray-500 hover:text-gray-700'} focus:outline-none`}
                onClick={() => setSidebarOpen(true)}
              >
                <Menu className="h-6 w-6" />
              </button>
              <h2 className={`text-xl font-semibold ${theme === 'dark' ? 'text-white' : 'text-gray-800'}`}>{getPageTitle()}</h2>
            </div>

            <div className="flex items-center space-x-4">
              {/* Search */}
              <div className={`hidden md:flex items-center ${theme === 'dark' ? 'bg-gray-800' : 'bg-gray-100'} rounded-lg px-3 py-1.5`}>
                <Search className={`h-4 w-4 ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`} />
                <input
                  type="text"
                  placeholder="Buscar..."
                  className={`bg-transparent border-none text-sm focus:outline-none ml-2 w-40 ${theme === 'dark' ? 'text-gray-300 placeholder-gray-500' : 'text-gray-700 placeholder-gray-400'}`}
                />
              </div>

              {/* Notifications */}
              <button className="text-gray-500 hover:text-gray-700 relative">
                <Bell className="h-5 w-5" />
                <span className="absolute top-0 right-0 h-2 w-2 rounded-full bg-orange-500"></span>
              </button>

              {/* Help */}
              <button className="hidden md:block text-gray-500 hover:text-gray-700">
                <HelpCircle className="h-5 w-5" />
              </button>

              {/* User menu */}
              <div className="relative">
                <button
                  className="flex items-center space-x-2 focus:outline-none"
                  onClick={() => setUserMenuOpen(!userMenuOpen)}
                >
                  <div className="w-8 h-8 rounded-full bg-gradient-to-r from-orange-500 to-amber-600 flex items-center justify-center text-white font-bold">
                    {user?.name?.charAt(0).toUpperCase()}
                  </div>
                  <span className="hidden md:block text-sm font-medium text-gray-700">{user?.name?.split(' ')[0]}</span>
                  <ChevronDown className="hidden md:block h-4 w-4 text-gray-500" />
                </button>

                {userMenuOpen && (
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200">
                    <Link to="/admin/profile" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      Mi Perfil
                    </Link>
                    <Link to="/admin/settings" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      Configuración
                    </Link>
                    <div className="border-t border-gray-200 my-1"></div>
                    <button
                      onClick={handleLogout}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      Cerrar Sesión
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </header>

        {/* Main content area */}
        <main className={`flex-1 overflow-y-auto ${theme === 'dark' ? 'bg-dark-bg' : 'bg-gray-50'}`}>
          {children}
        </main>
      </div>
    </div>
  );
};

export default AdminLayout;
