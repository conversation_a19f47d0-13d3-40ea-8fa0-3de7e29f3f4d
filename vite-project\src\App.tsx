import { BrowserRouter as Router, Routes, Route, Navigate, createBrowserRouter, RouterProvider } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import { ThemeProvider } from './contexts/ThemeContext';
import ProtectedRoute from './components/ProtectedRoute';
import AdminLayout from './components/layouts/AdminLayout';

// Páginas
import Login from './pages/Login';
import AccessDenied from './pages/AccessDenied';
import AdminDashboard from './pages/admin/Dashboard';
import EmployeeDashboard from './pages/employee/Dashboard';

// Empleados
import EmployeeList from './pages/admin/employees/EmployeeList';
import EmployeeForm from './pages/admin/employees/EmployeeForm';

// Inventario
import InventoryPage from './pages/admin/inventory/InventoryPage';
import CategoryPage from './pages/admin/inventory/CategoryPage';

// Apertura de Caja
import CashierPage from './pages/admin/cashier/CashierPage';

// Compras
import PurchasesPage from './pages/admin/purchases/PurchasesPage';
import NewPurchasePage from './pages/admin/purchases/NewPurchasePage';

// Reportes
import ReportsPage from './pages/admin/reports/ReportsPage';

// Configuración
import SettingsPage from './pages/admin/settings/SettingsPage';
import DniHistoryPage from './pages/admin/settings/DniHistoryPage';

// Crear el router con la bandera future para solucionar la advertencia
const router = createBrowserRouter([
  {
    path: "/",
    element: <Navigate to="/login" replace />
  },
  {
    path: "/login",
    element: <Login />
  },
  {
    path: "/access-denied",
    element: <AccessDenied />
  }
], {
  future: {
    v7_relativeSplatPath: true
  }
});

function App() {
  return (
    <AuthProvider>
      <ThemeProvider>
        <Router future={{ v7_relativeSplatPath: true }}>
          <Routes>
          {/* Ruta raíz - Redirige a login */}
          <Route path="/" element={<Navigate to="/login" replace />} />

          {/* Rutas públicas */}
          <Route path="/login" element={<Login />} />
          <Route path="/access-denied" element={<AccessDenied />} />

          {/* Rutas protegidas para administradores */}
          <Route element={<ProtectedRoute requireAdmin={true} />}>
            {/* Dashboard */}
            <Route path="/admin/dashboard" element={<AdminDashboard />} />

            {/* Empleados */}
            <Route path="/admin/employees" element={<EmployeeList />} />
            <Route path="/admin/employees/create" element={<EmployeeForm />} />
            <Route path="/admin/employees/edit/:id" element={<EmployeeForm />} />

            {/* Inventario */}
            <Route path="/admin/inventory" element={<InventoryPage />} />
            <Route path="/admin/inventory/add" element={<InventoryPage />} />
            <Route path="/admin/inventory/edit/:id" element={<InventoryPage />} />
            <Route path="/admin/inventory/low-stock" element={<InventoryPage />} />
            <Route path="/admin/inventory/categories" element={<CategoryPage />} />

            {/* Apertura de Caja */}
            <Route path="/admin/cashier" element={<CashierPage />} />
            <Route path="/admin/cashier/open" element={<CashierPage />} />
            <Route path="/admin/cashier/close" element={<CashierPage />} />
            <Route path="/admin/cashier/history" element={<CashierPage />} />

            {/* Compras */}
            <Route path="/admin/purchases" element={<PurchasesPage />} />
            <Route path="/admin/purchases/new" element={<NewPurchasePage />} />
            <Route path="/admin/purchases/edit/:id" element={<PurchasesPage />} />
            <Route path="/admin/purchases/details/:id" element={<PurchasesPage />} />

            {/* Reportes */}
            <Route path="/admin/reports" element={<ReportsPage />} />
            <Route path="/admin/reports/generate" element={<ReportsPage />} />
            <Route path="/admin/reports/sales" element={<ReportsPage />} />
            <Route path="/admin/reports/inventory" element={<ReportsPage />} />

            {/* Configuración */}
            <Route path="/admin/settings" element={<SettingsPage />} />
            <Route path="/admin/profile" element={<SettingsPage />} />
            <Route path="/admin/settings/dni-history" element={<DniHistoryPage />} />
          </Route>

          {/* Rutas protegidas para empleados */}
          <Route element={<ProtectedRoute />}>
            <Route path="/employee/dashboard" element={<EmployeeDashboard />} />
          </Route>

          {/* Ruta para cualquier otra dirección - Redirige a login */}
          <Route path="*" element={<Navigate to="/login" replace />} />
        </Routes>
        </Router>
      </ThemeProvider>
    </AuthProvider>
  );
}

export default App;
