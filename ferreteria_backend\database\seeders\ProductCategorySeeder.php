<?php

namespace Database\Seeders;

use App\Models\ProductCategory;
use Illuminate\Database\Seeder;

class ProductCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Herramientas',
                'description' => 'Herramientas manuales y eléctricas para construcción y reparación',
                'active' => true,
            ],
            [
                'name' => 'Materiales de Construcción',
                'description' => 'Cemento, ladrillos, arena y otros materiales para construcción',
                'active' => true,
            ],
            [
                'name' => 'Electricidad',
                'description' => 'Cables, interruptores, enchufes y otros materiales eléctricos',
                'active' => true,
            ],
            [
                'name' => 'Plomería',
                'description' => 'Tuberías, llaves, grifos y otros materiales para plomería',
                'active' => true,
            ],
            [
                'name' => 'Pinturas',
                'description' => 'Pinturas, barnices, esmaltes y accesorios para pintar',
                'active' => true,
            ],
            [
                'name' => 'Ferretería',
                'description' => 'Tornillos, clavos, tuercas, arandelas y otros artículos de ferretería',
                'active' => true,
            ],
            [
                'name' => 'Jardinería',
                'description' => 'Herramientas y materiales para jardinería',
                'active' => true,
            ],
            [
                'name' => 'Seguridad',
                'description' => 'Equipos de protección personal y seguridad',
                'active' => true,
            ],
        ];

        foreach ($categories as $category) {
            ProductCategory::create($category);
        }
    }
}
