import React from 'react';
import { useAuth } from '../../contexts/AuthContext';
import EmployeeLayout from '../../components/layouts/EmployeeLayout';

const EmployeeDashboard: React.FC = () => {
  const { user } = useAuth();

  return (
    <EmployeeLayout>
      <div className="p-6">
        <h1 className="text-2xl font-bold text-gray-800 mb-6">Dashboard de Empleado</h1>
        
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold text-gray-700 mb-4">Bienvenido, {user?.name}</h2>
          <p className="text-gray-600 mb-4">
            Este es tu panel de control como empleado de la ferretería.
          </p>
        </div>
        
        <div className="bg-blue-50 border border-blue-200 rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-blue-700 mb-3">Información del Empleado</h3>
          <div className="space-y-2">
            <p className="text-gray-700"><span className="font-medium">Nombre:</span> {user?.name}</p>
            <p className="text-gray-700"><span className="font-medium">Correo:</span> {user?.email}</p>
            <p className="text-gray-700"><span className="font-medium">Teléfono:</span> {user?.phone || 'No registrado'}</p>
            <p className="text-gray-700"><span className="font-medium">DNI:</span> {user?.dni || 'No registrado'}</p>
          </div>
        </div>
      </div>
    </EmployeeLayout>
  );
};

export default EmployeeDashboard;
