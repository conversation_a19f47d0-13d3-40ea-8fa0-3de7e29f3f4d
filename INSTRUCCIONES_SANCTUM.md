# Instrucciones para instalar Laravel Sanctum

Para solucionar el problema de autenticación, necesitas instalar Laravel Sanctum en tu proyecto. Sigue estos pasos:

1. Abre una terminal y navega a la carpeta del backend:
   ```bash
   cd ferreteria_backend
   ```

2. In<PERSON><PERSON> Laravel Sanctum usando Composer:
   ```bash
   composer require laravel/sanctum
   ```

3. Publica la configuración de Sanctum:
   ```bash
   php artisan vendor:publish --provider="Laravel\Sanctum\SanctumServiceProvider"
   ```

4. Ejecuta las migraciones para crear la tabla de tokens personales:
   ```bash
   php artisan migrate
   ```

5. Reinicia el servidor Laravel:
   ```bash
   php artisan serve
   ```

6. Reinicia el servidor de desarrollo de Vite:
   ```bash
   cd ../vite-project
   npm run dev
   ```

7. Intenta iniciar sesión nuevamente con las credenciales:
   - Email: <EMAIL>
   - Contraseña: password
