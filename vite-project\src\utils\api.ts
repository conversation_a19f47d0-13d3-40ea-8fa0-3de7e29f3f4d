import axios from 'axios';

// Crear una instancia de axios con la configuración base
const api = axios.create({
  baseURL: '/api', // Esto usará el proxy configurado en vite.config.ts
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  }
});

// Interceptor para agregar el token de autenticación a todas las solicitudes
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Rutas de la API
export const API_ROUTES = {
  // Autenticación
  LOGIN: '/login',
  LOGOUT: '/logout',

  // Empleados
  EMPLOYEES: '/employees',

  // Inventario
  PRODUCTS: '/products',
  PRODUCT_CATEGORIES: '/product-categories',

  // Caja
  CASH_REGISTERS: '/cash-registers',
  OPEN_CASH_REGISTER: '/cash-registers/open',
  CLOSE_CASH_REGISTER: (id: number) => `/cash-registers/${id}/close`,

  // Compras
  PURCHASES: '/sales',

  // Proveedores
  SUPPLIERS: '/suppliers',

  // Consulta DNI
  CONSULT_DNI: '/customer/consultar-dni',

  // Historial DNI
  DNI_HISTORY: '/dni-history'
};

export default api;
