<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

class AuthController extends Controller
{
    /**
     * Iniciar sesión y generar token de acceso.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function login(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        // Hardcoded admin user for testing
        if ($request->email === '<EMAIL>' && $request->password === 'password') {
            // Crear o encontrar el usuario admin
            $user = \App\Models\User::firstOrCreate(
                ['email' => '<EMAIL>'],
                [
                    'name' => 'Admin',
                    'password' => bcrypt('password'),
                    'role' => 'admin',
                ]
            );

            // Crear token simple (sin Sanctum por ahora)
            $token = 'admin-token-' . time() . '-' . $user->id;

            return response()->json([
                'success' => true,
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'role' => $user->role ?? 'admin',
                ],
                'token' => $token,
                'role' => $user->role ?? 'admin',
            ]);
        }

        // Si no es el admin, devolver error
        return response()->json([
            'success' => false,
            'message' => 'Credenciales incorrectas'
        ], 401);
    }

    /**
     * Obtener el usuario autenticado.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function user(Request $request)
    {
        // Por ahora, devolvemos el usuario admin hardcodeado
        return response()->json([
            'success' => true,
            'data' => [
                'id' => 1,
                'name' => 'Admin',
                'email' => '<EMAIL>',
                'role' => 'admin'
            ]
        ]);
    }

    /**
     * Cerrar sesión (revocar token).
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function logout(Request $request)
    {
        // Por ahora, solo devolvemos éxito
        return response()->json([
            'success' => true,
            'message' => 'Sesión cerrada correctamente'
        ]);
    }
}
