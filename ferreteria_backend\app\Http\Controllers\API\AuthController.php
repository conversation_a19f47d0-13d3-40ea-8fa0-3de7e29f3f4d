<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

class AuthController extends Controller
{
    /**
     * Iniciar sesión y generar token de acceso.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function login(Request $request)
    {
        // Hardcoded admin user for testing
        if ($request->email === '<EMAIL>' && $request->password === 'password') {
            // Crear respuesta de éxito
            return response()->json([
                'user' => [
                    'id' => 1,
                    'name' => 'Admin',
                    'email' => '<EMAIL>',
                    'role' => 'admin',
                ],
                'token' => 'admin-token-' . time(),
                'role' => 'admin',
            ]);
        }

        // Si no es el admin, devolver error
        return response()->json([
            'message' => 'Credenciales incorrectas'
        ], 401);
    }

    /**
     * Cerrar sesión (revocar token).
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function logout(Request $request)
    {
        // No necesitamos hacer nada en el servidor, el cliente eliminará el token
        return response()->json(['message' => 'Sesión cerrada correctamente']);
    }
}
