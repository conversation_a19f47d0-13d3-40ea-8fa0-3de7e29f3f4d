# Solución Alternativa para la Autenticación

He implementado una solución alternativa más simple para la autenticación, sin depender de Laravel Sanctum. Esta solución es adecuada para desarrollo, pero **no es segura para producción**.

## Cambios realizados:

1. **Controlador de autenticación simplificado**:
   - Generación de token simple usando base64
   - Método de logout simplificado

2. **Rutas API simplificadas**:
   - Sin middleware de autenticación
   - Acceso directo a los recursos

3. **Configuración de CORS actualizada**:
   - Permitir solicitudes desde el frontend
   - Configuración específica para desarrollo

4. **Servicio API en el frontend**:
   - Sin credenciales (cookies)
   - Configuración simplificada

## Cómo probar:

1. Reinicia el servid<PERSON>:
   ```bash
   cd ferreteria_backend
   php artisan serve
   ```

2. Reinicia el servidor de desarrollo de Vite:
   ```bash
   cd vite-project
   npm run dev
   ```

3. Intenta iniciar sesión con las credenciales:
   - Email: <EMAIL>
   - Contraseña: password

## Nota importante:

Esta solución es temporal y solo para desarrollo. Para un entorno de producción, deberías implementar una autenticación más segura utilizando Laravel Sanctum u otro sistema de autenticación robusto.
